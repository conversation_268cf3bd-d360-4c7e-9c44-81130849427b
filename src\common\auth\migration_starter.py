"""
Migration Starter - Bắt đầu migration từ hệ thống cũ sang Multi-Server Auth
"""

import logging
from typing import Optional
from src.common.auth import server_registry, token_manager, client_manager
from src.utils.config import Config
from src.utils.auth_qsettings import AuthQSettings

logger = logging.getLogger(__name__)

class MigrationHelper:
    """Helper class để hỗ trợ migration"""
    
    def __init__(self):
        self.auth_settings = AuthQSettings.get_instance()
        self.migration_completed = False
    
    def initialize_new_auth_system(self):
        """Khởi tạo hệ thống auth mới"""
        try:
            # 1. Setup default servers từ Config
            server_registry._setup_default_servers()
            
            # 2. Load tokens từ storage
            token_manager._load_tokens_from_storage()
            
            # 3. Migrate existing token nếu có
            self._migrate_existing_token()
            
            logger.info("New auth system initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize new auth system: {e}")
            return False
    
    def _migrate_existing_token(self):
        """Migrate token hiện tại sang hệ thống mới"""
        try:
            # L<PERSON>y token cũ từ AuthQSettings
            old_access_token = self.auth_settings.get_access_token()
            old_refresh_token = self.auth_settings.get_refresh_token()
            
            if old_access_token and old_refresh_token:
                logger.info("Found existing tokens, attempting migration...")
                
                # Xác định server type từ token hoặc config
                server_key = self._determine_server_key()
                
                if server_key:
                    # Tạo TokenInfo từ token cũ
                    from src.common.auth.token_manager import TokenInfo
                    import time
                    
                    token_info = TokenInfo(
                        access_token=old_access_token,
                        refresh_token=old_refresh_token,
                        expires_at=time.time() + 3600,  # Default 1 hour
                        server_type=self._get_server_type(server_key)
                    )
                    
                    # Lưu vào hệ thống mới
                    token_manager._tokens[server_key] = token_info
                    token_manager._save_tokens_to_storage()
                    
                    logger.info(f"Migrated existing token to server_key: {server_key}")
                    
        except Exception as e:
            logger.error(f"Token migration failed: {e}")
    
    def _determine_server_key(self) -> Optional[str]:
        """Xác định server key từ thông tin hiện có"""
        try:
            # Lấy server IP từ settings
            server_ip = self.auth_settings.load_ip_server()
            
            # Kiểm tra xem có phải là server đặc biệt không
            if hasattr(Config, 'EMS_API_URL') and Config.EMS_API_URL:
                if server_ip in Config.EMS_API_URL:
                    return 'gpstech'
                    
            if hasattr(Config, 'CYGATE_API_URL') and Config.CYGATE_API_URL:
                if server_ip in Config.CYGATE_API_URL:
                    return 'cygate'
            
            # Nếu là IP address thì tạo IP server key
            try:
                from ipaddress import ip_address
                ip_address(server_ip)
                server_port = self.auth_settings.load_port_server()
                return f"ip_{server_ip}_{server_port}"
            except:
                pass
                
            return None
            
        except Exception as e:
            logger.error(f"Failed to determine server key: {e}")
            return None
    
    def _get_server_type(self, server_key: str) -> str:
        """Lấy server type từ server key"""
        if server_key == 'gpstech':
            return 'gpstech'
        elif server_key == 'cygate':
            return 'cygate'
        elif server_key.startswith('ip_'):
            return 'ip_server'
        else:
            return 'custom'
    
    def create_legacy_wrapper(self, server_info_model):
        """Tạo wrapper để tương thích với code cũ"""
        from src.api.api_client import APIClient
        
        class LegacyAPIClientWrapper:
            """Wrapper để tương thích với APIClient cũ"""
            
            def __init__(self, server_info_model):
                self.server_info = server_info_model
                self.server_key = self._get_server_key()
                
                # Thử sử dụng hệ thống mới trước
                try:
                    self.new_client = client_manager.get_client(self.server_key)
                    self.use_new_system = True
                    logger.info(f"Using new auth system for {self.server_key}")
                except:
                    # Fall back to old system
                    self.old_client = APIClient(server_info_model)
                    self.use_new_system = False
                    logger.info(f"Falling back to old system for {self.server_key}")
            
            def _get_server_key(self):
                """Xác định server key từ server info"""
                server_ip = self.server_info.data.server_ip
                server_port = self.server_info.data.server_port
                
                # Check for special servers
                if hasattr(Config, 'EMS_API_URL') and Config.EMS_API_URL:
                    if server_ip in Config.EMS_API_URL:
                        return 'gpstech'
                        
                if hasattr(Config, 'CYGATE_API_URL') and Config.CYGATE_API_URL:
                    if server_ip in Config.CYGATE_API_URL:
                        return 'cygate'
                
                # IP server
                return f"ip_{server_ip}_{server_port}"
            
            def login(self, username, password, captcha=None, captcha_id=None):
                """Login method với compatibility"""
                if self.use_new_system:
                    if self.server_key.startswith('ip_'):
                        # IP server không cần login
                        return self._create_success_response()
                    else:
                        # OAuth login
                        success = token_manager.login(
                            self.server_key, username, password, captcha, captcha_id
                        )
                        return self._create_response(success)
                else:
                    return self.old_client.login(username, password, captcha, captcha_id)
            
            def get_cameras(self):
                """Get cameras với compatibility"""
                if self.use_new_system:
                    response = self.new_client.get('/api/cameras')
                    return response
                else:
                    return self.old_client.get_cameras()
            
            def _create_success_response(self):
                """Tạo response thành công giả"""
                import requests
                response = requests.Response()
                response.status_code = 200
                return response
            
            def _create_response(self, success: bool):
                """Tạo response từ boolean"""
                import requests
                response = requests.Response()
                response.status_code = 200 if success else 401
                return response
            
            # Proxy các method khác
            def __getattr__(self, name):
                if self.use_new_system:
                    # Map old method names to new client
                    method_mapping = {
                        'get_groups': lambda: self.new_client.get('/api/groups'),
                        'get_users': lambda: self.new_client.get('/api/users'),
                        'refresh_access_token': lambda: token_manager.get_valid_access_token(self.server_key),
                        # Add more mappings as needed
                    }
                    
                    if name in method_mapping:
                        return method_mapping[name]
                    else:
                        # Try to call method on new client
                        return getattr(self.new_client, name, None)
                else:
                    return getattr(self.old_client, name, None)
        
        return LegacyAPIClientWrapper(server_info_model)
    
    def setup_event_handlers(self):
        """Setup event handlers cho token management"""
        def on_token_refreshed(server_key: str, new_token: str):
            logger.info(f"Token refreshed for {server_key}")
            # TODO: Update WebSocket headers if needed
            
        def on_token_expired(server_key: str):
            logger.warning(f"Token expired for {server_key}")
            # TODO: Show notification to user
            
        def on_login_required(server_key: str):
            logger.warning(f"Login required for {server_key}")
            # TODO: Show login dialog
            
        # Connect signals
        token_manager.token_refreshed.connect(on_token_refreshed)
        token_manager.token_expired.connect(on_token_expired)
        token_manager.login_required.connect(on_login_required)
        
        logger.info("Event handlers setup completed")

# Global instance
migration_helper = MigrationHelper()

# Convenience functions
def initialize_auth_system():
    """Initialize new auth system"""
    return migration_helper.initialize_new_auth_system()

def create_compatible_api_client(server_info_model):
    """Create API client compatible with old system"""
    return migration_helper.create_legacy_wrapper(server_info_model)

def setup_token_events():
    """Setup token event handlers"""
    migration_helper.setup_event_handlers()
