import logging
from PySide6.Qt<PERSON>ore import QPoint, <PERSON><PERSON><PERSON>r, Qt, QEvent,Signal, QUrl, QObject, QThreadPool
from PySide6.QtPositioning import QGeoCoordinate
from src.common.widget.button_status_sidebar import ButtonStatusSidebar
from src.common.widget.custom_tab_widget.new_custom_tab_widget import NewCustomTabWidget
from src.common.widget.custom_titlebar.actionable_title_bar import ActionableTitleBar
from src.common.widget.custom_titlebar.custom_component.widget_button_system import WidgetButtonSystem
from src.common.widget.custom_titlebar.custom_component.widget_search_title import WidgetSearchTitle
from src.common.widget.notifications.notify import Notifications
from src.presentation.camera_screen.camera_bottom_toolbar import CameraBottomToolbarWidget
from src.presentation.camera_screen.tracking_camera_grid_widget import TrackingCameraGridWidget
from src.common.controller.main_controller import main_controller, connect_slot
from src.common.model.event_data_model import <PERSON>Model
from src.styles.style import Style
from src.utils.config import Config
from src.common.widget.event_bar import EventBar
from PySide6.QtGui import QGuiApplication, QIcon, QColor
from src.presentation.device_management_screen.widget.list_custom_widgets import LabelWidget
from src.presentation.camera_screen.camera_grid_widget import CameraGridWidget
from src.presentation.camera_screen.calendar_dialog import CalendarDialog
from src.presentation.camera_screen.main_tree_view_widget import MainTreeViewWidget
from src.common.model.camera_model import CameraModel,camera_model_manager
from src.common.model.group_model import group_model_manager, GroupModel
from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QApplication,QStyle, QSizePolicy, QSplitter, \
    QFrame, QStackedWidget, QPushButton,QMenu
from src.common.model.main_tree_view_model import TreeType,Status
from src.common.model.event_data_model import event_manager
from src.common.widget.button_state import GridButtonModel,original_list_data_grid
from src.common.controller.controller_manager import Controller,controller_manager
import random
from src.common.model.record_model import record_model_manager
from src.common.key_board.key_board_manager import KeyPressFilter
from src.common.key_board.shortcut_key import shortcut_key_model_manager
from src.common.joystick.joystick_manager import joystick_manager,WidgetType
from pyjoystick.sdl2 import Key
from src.utils.utils import Utils
from src.common.key_board.key_board_manager import key_board_manager
import time
from queue import Queue
from threading import Thread
from PySide6.QtQuickWidgets import QQuickWidget
from src.common.qml.models.timelinecontroller import TimeLineController,TimeLineManager
from src.common.controller.controller_manager import Controller, controller_manager
from src.common.qml.models.grid_model import GridModel
from src.presentation.camera_screen.managers.grid_manager import gridManager
from src.common.qml.models.camera_grid_item import CameraGridItem
import uuid
from src.common.model.screen_model import screenModelManager
from src.common.threads.sub_thread import SubThread
from src.common.threads.thread_pool_manager import threadPoolManager
from src.common.qml.models.common_enum import CommonEnum
from src.presentation.camera_screen.map.workers.map_cam_search import sortByEuclideanDistance
logger = logging.getLogger(__name__)

MAX_CAMERA_COUNT = 6
MAX_PADDING_COUNT = 8
class CameraScreen(QWidget):
    def __init__(self, parent=None, window_parent=None):
        super(CameraScreen, self).__init__(parent)
        self.mouse_pressed = False
        self.splitter = None
        self.is_sidebar_visible = True
        self.is_eventbar_visible = True
        self.parent = parent
        self.window_parent = window_parent
        self.list_screens = QApplication.screens()
        self.tracing_screen_index = None
        self.toggle_timeline_button = None
        self.list_tracing_camera = {}
        self.record_data = None
        self.calculate_layout()
        main_controller.list_parent['CameraScreen'] = self
        self.calendar_dialog = None
        self.timelinecontroller = None
        self.timeLineManager = None
        self.setup_ui()
        self.connect_slot()
        self.currentTime = 0
        threadPoolManager.create_pool("CameraScreen",max_threads=1)
        self.restyle_camera_screen()
        if main_controller.key_filter is None:
            main_controller.key_filter = KeyPressFilter()
        QApplication.instance().installEventFilter(main_controller.key_filter)
        joystick_manager.register(WidgetType.CameraScreen,self.key_received_signal)

    def process_event(self, event_id):
        while (time.time() - self.currentTime) < 2:
            logger.info(f'process_event {time.time() - self.currentTime}')
            time.sleep(0.1)
        self.currentTime = time.time()
        event: EventModel = event_manager.get_event(id=event_id)
        # Cấu hình hiển thị thông báo lên camera stream ở đây
        # camera_model = camera_model_manager.get_camera_model(id=event.cameraId)
        cam_id = event.get_property("cameraId")
        current_camera = camera_model_manager.get_camera_model(id=cam_id)
        if current_camera.data.get('coordinateLong') is not None and current_camera.data.get('coordinateLat') is not None:
            current_camera_lon = float(current_camera.data.get('coordinateLong'))
            current_camera_lat = float(current_camera.data.get('coordinateLat'))
            current_camera_coordinate = QGeoCoordinate(current_camera_lat, current_camera_lon)
            sorted_cam_list = sortByEuclideanDistance(cam_id, current_camera_coordinate, server_url=current_camera.get_property("server_ip"))
            route_list = sorted_cam_list[:MAX_CAMERA_COUNT]
            self.event:EventModel = event_manager.get_event(id=event_id)
            self.list_tracing_camera = {}
            self.list_tracing_camera[(0,0)] = camera_model_manager.get_camera_model(id=self.event.get_property("cameraId"))
            self.list_tracing_camera[(0,2)] = self.event
            for idx,distance_cam_dict in enumerate(route_list):
                if idx == 0:
                    self.list_tracing_camera[(1,2)] = distance_cam_dict['camera']
                elif idx == 1:
                    self.list_tracing_camera[(2,0)] = distance_cam_dict['camera']
                elif idx == 2:
                    self.list_tracing_camera[(2,1)] = distance_cam_dict['camera']
                elif idx == 3:
                    self.list_tracing_camera[(2,2)] = distance_cam_dict['camera']
            return ("Tracing Event", route_list)

    def callback_event(self,data):
        tab_name,camera_model = data
        for idx, cameraModel in self.list_tracing_camera.items():
            if isinstance(cameraModel,CameraModel):
                logger.info(f'callback_event = {idx}-{cameraModel.get_property("name")}')
        try:
            # Thực hiện tính toán và sắp xếp ở đây
            if len(self.list_tracing_camera) != 0:
                key, tracking_window, is_existing = self.find_value_in_dict(main_controller.tracing_windows_dict, None)
                logger.debug(f'key, tracking_window, is_existing {key, tracking_window, is_existing}')
                screen_idx = screenModelManager.getScreenIndex()
                if screen_idx is None:
                    return
                if screen_idx not in main_controller.tracing_windows_dict and not is_existing:
                    gridModel = self.create_tracking_grid_model(camera_model_list=self.list_tracing_camera,
                                                                            tab_name=tab_name)

                    tracking_window = TrackingCameraGridWidget(screen_index=screen_idx,
                                                            is_demo=True, tab_name=tab_name,
                                                            tracking_node_model=None,
                                                            gridModel=gridModel)
                    tuple_target = (tracking_window, None)
                    main_controller.tracing_windows_dict[screen_idx] = [tuple_target]
                    main_controller.tracing_windows_dict[screen_idx][0][0].showFullScreen()

                else:
                    for screen_idx, window_and_model in list(main_controller.tracing_windows_dict.items()):
                        logger.info(f"screen_idx = {screen_idx} - {window_and_model}")
                        for item in window_and_model:
                            if hasattr(item[0],"gridModel"):
                                gridModel:GridModel = item[0].gridModel
                                if gridModel is not None:
                                    self.diff_camera_list(new_list_camera_tracing=self.list_tracing_camera,
                                                        gridModel=gridModel)


            # self.item_alert_in_treeview(camera_model.data.name)

        except Exception as e:
            logger.error(f"Error in process_event: {e}")


    def connect_slot(self):
        connect_slot(
            (main_controller.complete_fetching_data,self.complete_fetching_data),
            (event_manager.add_event_signal,self.add_event_signal),
            (record_model_manager.add_records_signal,self.add_records_signal),
            (main_controller.open_map_in_tab_signal,self.open_map_in_tab),
            (main_controller.open_floor_in_tab_signal,self.open_floor_in_tab_signal),
            (main_controller.open_camera_in_tab_signal,self.open_items_in_tab),
            (main_controller.camera_clicked_signal,self.handle_color_preview),
            (main_controller.stop_live_camera_signal,self.stop_live_handle),
            (main_controller.stop_live_group_signal,self.stop_live_group_handle),
            (main_controller.open_camera_position_signal,self.open_camera_in_position),
            (shortcut_key_model_manager.add_shortcut_key_list_signal, self.add_shortcut_key_list_signal),
            (shortcut_key_model_manager.notification_signal, self.notification_signal),
            (main_controller.edit_floor_signal,self.edit_floor_signal),
            (main_controller.edit_map_signal,self.edit_map_handle)
            )

    def setup_ui(self):
        self.max_width_sidebar_default = int(self.screen_available_width * 0.2)
        self.min_width_sidebar_default = int(self.screen_available_width * 0.1)
        self.init_min_width_sidebar = int(self.screen_available_width * 0.08)
        self.min_height_timeline = int(self.screen_available_height * 0.12)
        self.main_treeview_widget = MainTreeViewWidget(parent=self)
        self.filter_text = LabelWidget(icon_clicked=self.icon_clicked)
        self.filter_text.hide()
        self.side_bar_widget = QWidget()
        self.side_bar_widget.setObjectName("side_bar_widget")
        self.side_bar_widget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.side_bar_layout = QVBoxLayout()
        self.side_bar_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.side_bar_layout.setContentsMargins(4, 4, 4, 0)
        self.side_bar_layout.setSpacing(0)
        self.widget_search_title_bar = WidgetSearchTitle(parent=self, window_parent=self.window_parent)
        self.widget_search_title_bar.search_widget.search_items_signal.connect(self.search_items)
        self.widget_search_title_bar.change_mode.change_mode_signal.connect(self.change_mode_trigger)
        self.side_bar_layout.addWidget(self.widget_search_title_bar)
        self.side_bar_layout.addWidget(self.filter_text)
        self.side_bar_layout.addWidget(self.main_treeview_widget)
        self.side_bar_widget.setLayout(self.side_bar_layout)
        self.new_custom_tab_widget = NewCustomTabWidget(parent=self, window_parent=self.window_parent)
        self.new_custom_tab_widget.callback_current_changed = self.callback_tab_changed
        self.new_custom_tab_widget.signal_add_tab_widget.connect(
            self.add_tab_widget)
        self.create_title_bar()
        self.center_view_widget = QWidget()
        self.center_view_widget.setObjectName("center_view_widget")
        self.center_view_layout = QHBoxLayout()
        self.center_view_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.center_view_layout.setSpacing(0)
        self.center_view_layout.setContentsMargins(0, 2, 0, 2)
        self.center_view_widget.setLayout(self.center_view_layout)
        self.center_view_widget.setSizePolicy(
            QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.center_stacked_widget = QStackedWidget()
        self.center_view_layout.addWidget(self.center_stacked_widget)
        self.load_tab_widget()
        if Config.ENABLE_EVENT_BAR:
            self.widget_event_bar = QWidget()
            self.layout_event_bar = QVBoxLayout()
            self.layout_event_bar.setContentsMargins(0, 0, 0, 0)
            self.event_bar = EventBar(parent=self)
            self.event_bar.setObjectName("event_bar")
            self.event_bar.setSizePolicy(
                QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
            self.widget_event_bar.setMaximumWidth(self.max_width_sidebar_default)
            self.widget_event_bar.setMinimumWidth(self.init_min_width_sidebar)
            self.layout_event_bar.addWidget(self.event_bar)
            self.widget_event_bar.setLayout(self.layout_event_bar)

            self.event_bar.combobox_hover_signal.connect(self.combobox_hover)
        self.splitter = QSplitter()
        self.splitter.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.splitter.setChildrenCollapsible(True)
        self.splitter.splitterMoved.connect(self.handle_splitter_moved)
        self.splitter.setStyleSheet(f"QSplitter::handle {{ background-color: {main_controller.get_theme_attribute('Color', 'main_background_splitter')}; }}")
        self.splitter.setContentsMargins(0, 0, 0, 0)
        self.splitter.setHandleWidth(10)
        self.side_bar_widget.setMaximumWidth(self.max_width_sidebar_default)
        self.side_bar_widget.setMinimumWidth(self.init_min_width_sidebar)
        self.splitter.addWidget(self.side_bar_widget)
        self.splitter.addWidget(self.center_view_widget)
        if Config.ENABLE_EVENT_BAR:
            self.splitter.addWidget(self.widget_event_bar)
        self.splitter.setStretchFactor(0, 2)  # Sidebar
        self.splitter.setStretchFactor(1, 18)  # Center view - giảm để dành chỗ cho event bar
        if Config.ENABLE_EVENT_BAR:
            self.splitter.setStretchFactor(2, 4)  # Event bar - tăng để vừa với event_type_row
        handle_sidebar = self.splitter.handle(1)
        handle_eventbar = self.splitter.handle(2)
        self.toggle_sidebar_button = ButtonStatusSidebar(handle_sidebar, init_svg_path=main_controller.get_theme_attribute('Image', 'icon_status_sidebar'))
        self.toggle_sidebar_button.installEventFilter(self)
        self.toggle_sidebar_button.setObjectName("toggle_sidebar_button")
        self.toggle_sidebar_button.clicked.connect(lambda: self.toggle_sidebar(True))
        layout_status_active_sidebar = QVBoxLayout()
        layout_status_active_sidebar.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout_status_active_sidebar.setContentsMargins(0, 0, 0, 0)
        layout_status_active_sidebar.addWidget(self.toggle_sidebar_button)
        self.toggle_eventbar_button = ButtonStatusSidebar(handle_eventbar, main_controller.get_theme_attribute('Image', 'icon_status_sidebar'))
        self.toggle_eventbar_button.installEventFilter(self)
        self.toggle_eventbar_button.setObjectName("toggle_eventbar_button")
        self.toggle_eventbar_button.clicked.connect(lambda: self.toggle_event_bar(True))
        layout_status_active_eventbar = QVBoxLayout()
        layout_status_active_eventbar.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout_status_active_eventbar.setContentsMargins(0, 0, 0, 0)
        layout_status_active_eventbar.addWidget(self.toggle_eventbar_button)
        handle_sidebar.setLayout(layout_status_active_sidebar)
        handle_eventbar.setLayout(layout_status_active_eventbar)
        self.main_splitter = QSplitter(self)
        self.main_splitter.setOrientation(Qt.Orientation.Vertical)
        self.main_splitter.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.main_splitter.setChildrenCollapsible(True)
        self.main_splitter.splitterMoved.connect(self.handle_splitter_moved)
        self.main_splitter.setStyleSheet(
            f"""QSplitter::handle {{ 
                            background-color: {main_controller.get_theme_attribute('Color', 'main_background')};
                            border-top: 1px solid {main_controller.get_theme_attribute('Color', 'main_border')};
                            border-bottom: 1px solid {main_controller.get_theme_attribute('Color', 'main_border')};
                        }}"""
        )
        self.main_splitter.setContentsMargins(0, 0, 0, 0)
        self.main_splitter.setHandleWidth(10)
        self.main_splitter.addWidget(self.splitter)
        self.timelinecontrol_qml = self.create_timeline_widget()
        self.main_splitter.addWidget(self.timelinecontrol_qml)
        self.main_splitter.setStretchFactor(0, 70)
        self.main_splitter.setStretchFactor(1, 30)
        self.timelinecontrol_qml.setMaximumHeight(self.min_height_timeline + 50)
        self.timelinecontrol_qml.setMinimumHeight(self.min_height_timeline)
        handle_timeline = self.main_splitter.handle(1)
        image = main_controller.get_theme_attribute('Image', 'icon_status_timeline')
        self.toggle_timeline_button = ButtonStatusSidebar(handle_timeline, image)
        self.toggle_timeline_button.setFixedSize(20,10)
        self.toggle_timeline_button.installEventFilter(self)
        self.toggle_timeline_button.setObjectName("toggle_timeline_button")
        self.toggle_timeline_button.clicked.connect(lambda: self.toggle_timeline(False))
        layout_status_active_timeline = QVBoxLayout()
        layout_status_active_timeline.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout_status_active_timeline.setContentsMargins(0, 0, 0, 0)
        layout_status_active_timeline.addWidget(self.toggle_timeline_button)
        handle_timeline.setLayout(layout_status_active_timeline)
        self.main_layout = QVBoxLayout()
        self.main_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        margin_size = 0
        self.main_layout.setContentsMargins(
            margin_size, margin_size, margin_size, margin_size)
        self.main_layout.setSpacing(margin_size)
        self.main_layout.addWidget(self.widget_title_bar)
        self.main_layout.addWidget(self.main_splitter)
        self.setLayout(self.main_layout)
        self.showEvent = self.on_show_event
        self.resizeEvent = self.on_resize_event
        sizes = self.main_splitter.sizes()
        if Utils.is_windows():
            sizes[1] = 0
        self.main_splitter.setSizes(sizes)

    def handle_joystick_button_press(self,key):
        key_event = joystick_manager.get_key_event(key)
        logger.debug(f'handle_joystick_button_press = {key.keytype} {key.number} {key.value} {key_event} {key_event}')
        if key_event == Qt.Key.Key_Minus:
            self.func_zoom_out(key)
        elif key_event == Qt.Key.Key_Equal:
            self.func_zoom_in(key)
        else:
            # Xử lý các sự kiện phím bấm khác trên Joystick
            # Tạm thời chỉ nhận sự kiến ấn Phím từ Joystick, bỏ qua sự kiện nhả phím trước
            if key.value == 1:
                if int(key_event) in shortcut_key_model_manager.shortcut_key_list:
                    key_board_manager.keys.clear()
                    key_board_manager.keys[str(key_event)] = {'key_list': []}
                elif str(key_event) in key_board_manager.shortcut_keys:
                    if key_event == Qt.Key.Key_Alt:
                        key_board_manager.stop_timer_list()
                        key_board_manager.keys.clear()
                        key_board_manager.keys[str(key_event)] = {'screen_number': [],'number_camera':[],'screen_selected': None}
                        # show number man hinh chinh
                        custom_tab_widget = main_controller.list_parent['CustomTabWidget']
                        widget = custom_tab_widget.getCurrentWidget()
                        widget.show_screen_index(0)
                        widget.show_item_index()
                        ########################
                        key_board_manager.keys[str(key_event)][0] = custom_tab_widget
                        screen_list = key_board_manager.shortcut_keys[str(Qt.Key.Key_Alt)]['func_start_key']()
                        if len(screen_list) > 0:
                            for index,screen in enumerate(screen_list):
                                key_board_manager.keys[str(key_event)][index + 1] = screen
                                virtual_window_widget = screen[1]
                                virtual_window_widget.show_screen_index(index + 1)
                                virtual_window_widget.show_item_index()     
                else:
                    start_key = key_board_manager.get_start_key()
                    if start_key is not None:
                        if start_key == str(Qt.Key.Key_Slash) or start_key == str(Qt.Key.Key_Asterisk):
                            if key_event != Qt.Key.Key_Return and key_event != Qt.Key.Key_Enter:
                                for idx,value in key_board_manager.keys.items():
                                    value['key_list'].append(key_event)
                                    break
                            else:
                                for idx,value in key_board_manager.keys.items():
                                    if len(value['key_list']) > 0:
                                        number = key_board_manager.keys_to_number(number_list=value['key_list'])
                                        ok = shortcut_key_model_manager.run_func(start_key=int(idx),id = number)
                                        if not ok:
                                            shortcut_key_model_manager.notification_signal.emit("This ShortcutID does not exist.")  
                                        break
                                key_board_manager.keys = {}
                        elif start_key == str(Qt.Key.Key_Alt):
                            if key_event != Qt.Key.Key_Return and key_event != Qt.Key.Key_Enter:
                                for idx,value in key_board_manager.keys.items():
                                    if key_board_manager.is_number(key=key_event):
                                        # Chỉ xử lý key là ký tự number
                                        value['screen_number'].append(key_event)
                                        number = key_board_manager.keys_to_number(number_list=value['screen_number'])
                                        if number in key_board_manager.keys[start_key] and value['screen_selected'] is None:
                                            value['screen_selected'] = number
                                            # tim thấy số màn hình mà nguoi dùng đã chọn
                                            if number == 0:
                                                custom_tab_widget = key_board_manager.keys[start_key][0]
                                                widget = custom_tab_widget.getCurrentWidget()
                                                widget.change_screen_index_color()
                                                widget_selected = grid_item_selected.data['widget']
                                                if widget_selected is not None:
                                                    # bỏ focus camera item trước đó
                                                    if hasattr(widget_selected.stack_item, 'grid_item_unclicked'):
                                                        widget_selected.stack_item.grid_item_unclicked()
                                            else:
                                                virtual_window = key_board_manager.keys[start_key][number]
                                                screen_index = virtual_window[0]
                                                virtual_window_widget = virtual_window[1]
                                                virtual_window_widget.change_screen_index_color()
                                                widget_selected = grid_item_selected.data['widget']
                                                if widget_selected is not None:
                                                    # bỏ focus camera item trước đó
                                                    if hasattr(widget_selected.stack_item, 'grid_item_unclicked'):
                                                        widget_selected.stack_item.grid_item_unclicked()
                                        else:
                                            # Trường hợp ko tìm thấy mình hình thì check xem phím tắt trước đã tìm thấy chưa 
                                            if value['screen_selected'] is not None:
                                                # Tiếp tục nhận key để tìm camera item trong grid
                                                if value['screen_selected'] == 0:
                                                    value['number_camera'].append(key_event)
                                                    number = key_board_manager.keys_to_number(number_list=value['number_camera'])
                                                    screen_widget = key_board_manager.keys[start_key][value['screen_selected']]
                                                    widget = screen_widget.getCurrentWidget()
                                                    widget.find_item_index(number)
                                                else:
                                                    # man hinh virtual
                                                    value['number_camera'].append(key_event)
                                                    number = key_board_manager.keys_to_number(number_list=value['number_camera'])
                                                    vitual_window_widget = key_board_manager.keys[start_key][value['screen_selected']]
                                                    vitual_window_widget[1].find_item_index(number)
                                        break
                    else:
                        
                        widget_selected = grid_item_selected.data['widget']
                        screen = grid_item_selected.data['screen']
                        if widget_selected is not None:
                            if key_event == Qt.Key.Key_6:
                                screen =  grid_item_selected.data['screen']
                                type = grid_item_selected.data['type']
                                if screen == 'Main':
                                    custom_tab_widget = main_controller.list_parent['CustomTabWidget']
                                    widget = custom_tab_widget.getCurrentWidget()
                                    widget.process_change_item(action = 6)
                                elif screen is not None and screen in main_controller.list_parent:
                                    camera_grid_base = main_controller.list_parent[screen][1]
                                    camera_grid_base.process_change_item(action = 6)
                            elif key_event == Qt.Key.Key_4:
                                screen =  grid_item_selected.data['screen']
                                type = grid_item_selected.data['type']
                                if screen == 'Main':
                                    custom_tab_widget = main_controller.list_parent['CustomTabWidget']
                                    widget = custom_tab_widget.getCurrentWidget()
                                    widget.process_change_item(action = 4)
                                elif screen is not None and screen in main_controller.list_parent:
                                    camera_grid_base = main_controller.list_parent[screen][1]
                                    camera_grid_base.process_change_item(action = 4)
                            elif key_event == Qt.Key.Key_2:
                                screen =  grid_item_selected.data['screen']
                                type = grid_item_selected.data['type']
                                if screen == 'Main':
                                    custom_tab_widget = main_controller.list_parent['CustomTabWidget']
                                    widget = custom_tab_widget.getCurrentWidget()
                                    widget.process_change_item(action = 2)
                                elif screen is not None and screen in main_controller.list_parent:
                                    camera_grid_base = main_controller.list_parent[screen][1]
                                    camera_grid_base.process_change_item(action = 2)
                            elif key_event == Qt.Key.Key_8:
                                screen =  grid_item_selected.data['screen']
                                type = grid_item_selected.data['type']
                                if screen == 'Main':
                                    custom_tab_widget = main_controller.list_parent['CustomTabWidget']
                                    widget = custom_tab_widget.getCurrentWidget()
                                    widget.process_change_item(action = 8)
                                elif screen is not None and screen in main_controller.list_parent:
                                    camera_grid_base = main_controller.list_parent[screen][1]
                                    camera_grid_base.process_change_item(action = 8)


    def key_received_signal(self,key):
        logger.debug(f'key_received_signal = {key.keytype} {key.number} {key.value}')
        try:
            if joystick_manager.widget_type == WidgetType.CameraScreen:
                camera_widget:CameraWidget = grid_item_selected.data.get('widget', None)
                if key.keytype == Key.BUTTON:
                    self.handle_joystick_button_press(key)
                elif key.keytype == Key.AXIS:
                    if camera_widget is not None:
                        if key.value < 0.01 and key.value > -0.01:
                            self.key_pressed = False
                            if key.number == 2:
                                if camera_widget.camera_model.get_property("type") == "AVIGILON":
                                    data = {
                                        "cameraId": camera_widget.camera_model.get_property('id'),
                                        "endPoint": "/camera/commands/pan-tilt-zoom",
                                        "requestData": {
                                            "continuous": {
                                                "panAmount": 0,
                                                "tiltAmount": 0,
                                                "zoomAmount": 0,
                                                "action": "STOP"
                                                }
                                        }
                                    }
                                    camera_widget.controller.forward_avigilon_ptz(cameraId = data["cameraId"],requestData=data["requestData"])
                                else:
                                    camera_widget.current_joystick_msg = {"type": 'button_zoom','speed': 0}
                                    if not camera_widget.process_joystick_queue_thread.isRunning():
                                        camera_widget.process_joystick_queue_thread.start()
                            elif key.number == 0 or key.number == 1:
                                if camera_widget.camera_model.get_property("ptzCap") is not None and len(camera_widget.camera_model.get_property("ptzCap")) >0:
                                    # message = {"type": 'axis',"x": 0,"y": 0, "profileToken": self.profileToken}
                                    # self.ptz_onvif.put_joystick_queue(message)
                                    if camera_widget.camera_model.get_property("type") == "AVIGILON":
                                        data = {
                                            "cameraId": camera_widget.camera_model.get_property('id'),
                                            "endPoint": "/camera/commands/pan-tilt-zoom",
                                            "requestData": {
                                                "continuous": {
                                                    "panAmount": 0,
                                                    "tiltAmount": 0,
                                                    "zoomAmount": 0,
                                                    "action": "STOP"
                                                    }
                                            }
                                        }
                                        camera_widget.controller.forward_avigilon_ptz(cameraId = data["cameraId"],requestData=data["requestData"])
                                    else:
                                        camera_widget.current_joystick_msg = {"type": 'axis',"x": 0,"y": 0}
                                        if not camera_widget.process_joystick_queue_thread.isRunning():
                                            camera_widget.process_joystick_queue_thread.start()
                                    
                                
                        else:
                            if key.number == 0:
                                # if self.ptz_onvif != None and self.profileToken != None and self.is_ptz_arrow_configuration:
                                if camera_widget.camera_model.get_property("ptzCap") is not None and len(camera_widget.camera_model.get_property("ptzCap")) >0:
                                    # message = {"type": 'axis',"x": key.value,"y": 0, "profileToken": self.profileToken}
                                    # self.ptz_onvif.put_joystick_queue(message)
                                    if camera_widget.camera_model.get_property("type") == "AVIGILON":
                                        data = {
                                            "cameraId": camera_widget.camera_model.get_property('id'),
                                            "endPoint": "/camera/commands/pan-tilt-zoom",
                                            "requestData": {
                                                "continuous": {
                                                    "panAmount": key.value,
                                                    "tiltAmount": 0,
                                                    "zoomAmount": 0,
                                                    "action": "START"
                                                    }
                                            }
                                        }
                                        camera_widget.controller.forward_avigilon_ptz(cameraId = data["cameraId"],requestData=data["requestData"])
                                    else:
                                        camera_widget.current_joystick_msg = {"type": 'axis',"x": key.value,"y": 0}
                                        if not camera_widget.process_joystick_queue_thread.isRunning():
                                            camera_widget.process_joystick_queue_thread.start()
                            elif key.number == 1:
                                # if self.ptz_onvif != None and self.profileToken != None and self.is_ptz_arrow_configuration:
                                if camera_widget.camera_model.get_property("ptzCap") is not None and len(camera_widget.camera_model.get_property("ptzCap")) >0:
                                    y = -key.value
                                    # message = {"type": 'axis',"x": 0,"y": y, "profileToken": self.profileToken}
                                    # self.ptz_onvif.put_joystick_queue(message)
                                    if camera_widget.camera_model.get_property("type") == "AVIGILON":
                                        data = {
                                            "cameraId": camera_widget.camera_model.get_property('id'),
                                            "endPoint": "/camera/commands/pan-tilt-zoom",
                                            "requestData": {
                                                "continuous": {
                                                    "panAmount": 0,
                                                    "tiltAmount": -y,
                                                    "zoomAmount": 0,
                                                    "action": "START"
                                                    }
                                            }
                                        }
                                        camera_widget.controller.forward_avigilon_ptz(cameraId = data["cameraId"],requestData=data["requestData"])
                                    else:
                                        camera_widget.current_joystick_msg = {"type": 'axis',"x": 0,"y": y}
                                        if not camera_widget.process_joystick_queue_thread.isRunning():
                                            camera_widget.process_joystick_queue_thread.start()
                            elif key.number == 2:
                                    # if self.ptz_onvif != None and self.profileToken != None:
                                    if camera_widget.camera_model.get_property("ptzCap") is not None and len(camera_widget.camera_model.get_property("ptzCap")) >0:
                                        # message = {"type": 'button_zoom','speed': key.value, "profileToken": self.profileToken}
                                        # self.ptz_onvif.put_joystick_queue(message)
                                        if camera_widget.camera_model.get_property("type") == "AVIGILON":
                                            data = {
                                                "cameraId": camera_widget.camera_model.get_property('id'),
                                                "endPoint": "/camera/commands/pan-tilt-zoom",
                                                "requestData": {
                                                    "continuous": {
                                                        "panAmount": 0,
                                                        "tiltAmount": 0,
                                                        "zoomAmount": key.value,
                                                        "action": "START"
                                                        }
                                                }
                                            }
                                            camera_widget.controller.forward_avigilon_ptz(cameraId = data["cameraId"],requestData=data["requestData"])
                                        else:
                                            camera_widget.current_joystick_msg = {"type": 'button_zoom','speed': key.value}
                                            if not camera_widget.process_joystick_queue_thread.isRunning():
                                                camera_widget.process_joystick_queue_thread.start()
        
        except Exception as e:
            logger.debug(f'key_received_signal error: {e}')

    def func_zoom_in(self,key):
        camera_widget:CameraWidget = grid_item_selected.data.get('widget', None)
        if camera_widget is not None:
            logger.info(f'func_zoom_in')
            if camera_widget.camera_model.get_property("ptzCap") is not None and len(camera_widget.camera_model.get_property("ptzCap")) >0:
                if camera_widget.camera_model.get_property("type") == "AVIGILON":
                    data = {
                        "cameraId": camera_widget.camera_model.get_property('id'),
                        "endPoint": "/camera/commands/pan-tilt-zoom",
                        "requestData": {
                            "continuous": {
                                "panAmount": 0,
                                "tiltAmount": 0,
                                "zoomAmount": key.value,
                                "action": "START"
                                }
                        }
                    }
                    camera_widget.controller.forward_avigilon_ptz(cameraId = data["cameraId"],requestData=data["requestData"])
                else:
                    camera_widget.current_joystick_msg = {"type": 'button_zoom','speed': key.value}
                    if not camera_widget.process_joystick_queue_thread.isRunning():
                        camera_widget.process_joystick_queue_thread.start()

    def func_zoom_out(self,key): # Button zoomout
        camera_widget:CameraWidget = grid_item_selected.data.get('widget', None)
        if camera_widget is not None:
            logger.info(f'func_zoom_out')
            if camera_widget.camera_model.get_property("ptzCap") is not None and len(camera_widget.camera_model.get_property("ptzCap")) >0:
                speed = 0 - key.value
                if camera_widget.camera_model.get_property("type") == "AVIGILON":
                    data = {
                        "cameraId": camera_widget.camera_model.get_property('id'),
                        "endPoint": "/camera/commands/pan-tilt-zoom",
                        "requestData": {
                            "continuous": {
                                "panAmount": 0,
                                "tiltAmount": 0,
                                "zoomAmount": speed,
                                "action": "START"
                                }
                        }
                    }
                    camera_widget.controller.forward_avigilon_ptz(cameraId = data["cameraId"],requestData=data["requestData"])
                else:
                    camera_widget.current_joystick_msg = {"type": 'button_zoom','speed': speed}
                    if not camera_widget.process_joystick_queue_thread.isRunning():
                        camera_widget.process_joystick_queue_thread.start()
            
    def add_records_signal(self,data):
        logger.debug(f'add_records_signal = {data}')
        sizes = self.main_splitter.sizes()
        if sizes[1] == 0:
            sizes[1] = self.min_height_timeline
            sizes[0] = sizes[0] - self.min_height_timeline
        self.main_splitter.setSizes(sizes)

    def timeout(self):
        self.timer = QTimer(self)
        self.timer.setInterval(1000)
        self.timer.timeout.connect(self.apply_auto_next)
        self.timer.start()

    def send_auto_next(self):
        self.timeout()

    def apply_auto_next(self):
        random_number = random.randint(1, 3)
        if random_number == 1:
            self.send_ai_event()
        elif random_number == 2:
            self.send_ai_event2()
        elif random_number == 3:
            self.send_ai_event3()

    def send_ai_event(self):
        data = {
            "id": "d634926d-0dae-4dc1-bf79-10352b4e3cce",
            "createdAtLocalDate": "2025-06-23 16:21:12.043905",
            "eventType": "RECOGNITION",
            "imageUrl": "https://ems-lab-storage.ai-vlab.com/images/images/72adac41-29b5-4e8b-b6b8-e8741da14a10/human/2025_06_23/16_21/1750670471590/371f8633-56cc-4d61-aaad-c9ae1f47dacb.jpg",
            "name": "Tùng",
            "type": "HUMAN",
            "status": "UNKNOWN",
            "ioId": "2824bfc8-52d3-451d-9434-a5adc30ff049",
            "ioGroups": [],
            "warningConfig": [
                59
            ],
            "cameraId": "72adac41-29b5-4e8b-b6b8-e8741da14a10",
            "cameraLocation": "Quốc lộ 3, Huyện Đông Anh, Hà Nội, Việt Nam",
            "cameraName": "CAMERA //118.70.120.81:5541/",
            "directionType": "",
            "humanInfo": {
                "face": {
                    "age": "20-29",
                    "gender": "male",
                    "race": "east_asian"
                },
                "person": {
                    "hair": "",
                    "upper": {
                        "color": None,
                        "style": ""
                    },
                    "lower": {
                        "color": None,
                        "style": ""
                    },
                    "hat": False,
                    "glasses": False,
                    "bag": "",
                    "direction": "",
                    "holdObjectsInFront": False
                }
            },
            "imageCropUrl": "https://ems-lab-storage.ai-vlab.com/images/images/72adac41-29b5-4e8b-b6b8-e8741da14a10/human/2025_06_23/16_21/1750670471590/62f2af51-1883-4894-966b-44f36954825e.jpg",
            "imageFullUrl": "https://minio.gpstech.vn/images/images/human/0b1375e1-126c-4d29-863f-d3c1fedb219a/2024_12_20/11_38/1734669480048/a0b445ee-c62e-476c-9239-eafe77dd9830.jpg",
            "imageLicensePlateUrl": ""}
        event_manager.add_event_data.emit(data)

    def send_ai_event2(self):
        data = {
            "id": "b39f47e7-dce9-4a78-96b6-7a9039b89df8",
            "createdAtLocalDate": "2025-05-13 07:13:37.041160",
            "event": "",
            "imageUrl": "https://minio.gpstech.vn/images/human/36cb66d3-ff17-4a1a-b924-54e4be560ab1/2024_05_23/15_18/1716452321714/cfe13270-0fca-454e-847a-83884418d4b5.jpg",
            "name": "Tùng",
            "type": "HUMAN",
            "eventType": "RECOGNITION",
            "status": "UNKNOWN",
            "ioId": "2824bfc8-52d3-451d-9434-a5adc30ff049",
            "ioGroups": [],
            "warningConfig": [
                59
            ],
            "cameraId": "554da364-a28a-4064-9fe1-18a268eec970",
            "cameraLocation": "Quốc lộ 3, Huyện Đông Anh, Hà Nội, Việt Nam",
            "cameraName": "CHECK-OUT T3 MAP",
            "directionType": "",
            "humanInfo": {
                "face": {
                    "age": "20-29",
                    "gender": "male",
                    "race": "east_asian"
                },
                "person": {
                    "hair": "",
                    "upper": {
                        "color": None,
                        "style": ""
                    },
                    "lower": {
                        "color": None,
                        "style": ""
                    },
                    "hat": False,
                    "glasses": False,
                    "bag": "",
                    "direction": "",
                    "holdObjectsInFront": False
                }
            },
            "imageFullUrl": "https://minio.gpstech.vn/images/human/36cb66d3-ff17-4a1a-b924-54e4be560ab1/2024_05_23/15_18/1716452321627/7bb9d087-715f-4de6-b0c6-60a21676a945.jpg",
            "imageLicensePlateUrl": ""}
        event_manager.add_event_data.emit(data)

    def send_ai_event3(self):
        data = {
            "id": "b39f47e7-dce9-4a78-96b6-7a9039b89df7",
            "createdAtLocalDate": "2025-05-13 07:13:37.041160",
            "event": "",
            "imageUrl": "https://minio.gpstech.vn/images/human/36cb66d3-ff17-4a1a-b924-54e4be560ab1/2024_05_23/15_18/1716452321714/cfe13270-0fca-454e-847a-83884418d4b5.jpg",
            "name": "Tùng",
            "type": "HUMAN",
            "eventType": "RECOGNITION",
            "status": "UNKNOWN",
            "ioId": "2824bfc8-52d3-451d-9434-a5adc30ff049",
            "ioGroups": [],
            "warningConfig": [
                59
            ],
            "cameraId": "4c99fc26-81c9-4b67-b380-be22d302d244",
            "cameraLocation": "Quốc lộ 3, Huyện Đông Anh, Hà Nội, Việt Nam",
            "cameraName": "CHECK-IN T3 MAP",
            "directionType": "",
            "humanInfo": {
                "face": {
                    "age": "20-29",
                    "gender": "male",
                    "race": "east_asian"
                },
                "person": {
                    "hair": "",
                    "upper": {
                        "color": None,
                        "style": ""
                    },
                    "lower": {
                        "color": None,
                        "style": ""
                    },
                    "hat": False,
                    "glasses": False,
                    "bag": "",
                    "direction": "",
                    "holdObjectsInFront": False
                }
            },
            "imageFullUrl": "https://minio.gpstech.vn/images/human/36cb66d3-ff17-4a1a-b924-54e4be560ab1/2024_05_23/15_18/1716452321627/7bb9d087-715f-4de6-b0c6-60a21676a945.jpg",
            "imageLicensePlateUrl": ""}
        event_manager.add_event_data.emit(data)

    def add_shortcut_key_list_signal(self,data):
        pass

    def notification_signal(self,data):
        Notifications(parent=main_controller.list_parent['CameraScreen'],
                              title=self.tr('This ShortcutID does not exist.'), icon=Style.PrimaryImage.info_result)

    def create_divider(self):
        divider = QFrame()
        divider.setFixedSize(1, 20)
        divider.setFrameShape(QFrame.Shape.VLine)
        divider.setFrameShadow(QFrame.Shadow.Sunken)
        divider.setStyleSheet(f"background-color: {Style.PrimaryColor.on_background}")
        return divider

    def create_title_bar(self):
        self.widget_button_system = WidgetButtonSystem(parent=self, window_parent=self.window_parent)
        self.layout_title_bar = QHBoxLayout()
        self.layout_title_bar.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.layout_title_bar.setContentsMargins(0, 0, 0, 0)
        self.layout_title_bar.setSpacing(0)
        self.camera_bottom_toolbar = CameraBottomToolbarWidget(self)
        self.button_widget = QWidget()
        self.button_layout = QHBoxLayout(self.button_widget)
        self.button_layout.setAlignment(Qt.AlignmentFlag.AlignRight)
        self.button_layout.setSpacing(0)
        self.button_layout.setContentsMargins(0, 0, 0, 0)
        divider1 = self.create_divider()
        divider2 = self.create_divider()
        if Config.ENABLE_WARNING_ALERT_CAMERA:
            self.layout_object_detect = QHBoxLayout()
            self.layout_object_detect.setContentsMargins(0, 0, 4, 0)
            self.layout_object_detect.setAlignment(Qt.AlignmentFlag.AlignLeft)
            if Config.CONFIG_TEST_WARNING:
                self.auto_next = QPushButton('Auto Next')
                self.auto_next.clicked.connect(self.send_auto_next)
                self.layout_object_detect.addWidget(self.auto_next)
                self.button_test = QPushButton('Event 1')
                self.button_test.clicked.connect(self.send_ai_event)
                self.layout_object_detect.addWidget(self.button_test)
                self.button_test2 = QPushButton('Event 2')
                self.button_test2.clicked.connect(self.send_ai_event2)
                self.layout_object_detect.addWidget(self.button_test2)
                self.button_test3 = QPushButton('Event 3')
                self.button_test3.clicked.connect(self.send_ai_event3)
                self.layout_object_detect.addWidget(self.button_test3)
            self.widget_object_tracking = QWidget()
            self.widget_object_tracking.setLayout(self.layout_object_detect)
            self.button_layout.addWidget(self.widget_object_tracking, 5)
        self.button_layout.addWidget(self.camera_bottom_toolbar, 3)
        self.button_layout.addWidget(divider2)
        self.button_layout.addWidget(self.widget_button_system, 2)
        self.layout_title_bar.addWidget(self.new_custom_tab_widget, 9)
        self.layout_title_bar.addWidget(self.button_widget, 1)
        self.widget_title_bar = ActionableTitleBar(parent=self, window_parent=self.window_parent)
        self.widget_title_bar.setLayout(self.layout_title_bar)
        self.widget_title_bar.setFixedHeight(40)
    
    def create_timeline_widget(self):
        self.timelinecontroller = TimeLineController()
        self.timeLineManager = TimeLineManager(parent=self)
        self.timeLineManager.timeLineController = self.timelinecontroller
        qml_widget = QQuickWidget()
        qml_widget.engine().rootContext().setContextProperty('timeLineManager', self.timeLineManager)
        qml_widget.setSource(QUrl("qrc:src/common/qml/videoplayback/TimeLineControl.qml"))
        qml_widget.setResizeMode(QQuickWidget.SizeRootObjectToView)
        return qml_widget
    
    def combobox_hover(self, data):
        screen_index, screen = data
        self.main_treeview_widget.show_border_triggered(screen_index=screen_index, screen=screen)
        QTimer.singleShot(3000, lambda: self.main_treeview_widget.handle_menu_about_to_hide())

    def toggle_sidebar_move(self, event):
        button_pos = self.toggle_sidebar_button.mapToGlobal(event.position())
        handle_pos = self.handle_sidebar.mapToGlobal(self.handle_sidebar.rect().center())
        move_delta = button_pos.x() - handle_pos.x()
        new_x = handle_pos.x() + move_delta
        new_pos = QPoint(new_x, handle_pos.y())
        int_pos = int(new_pos.x())
        self.splitter.moveSplitter(int_pos, 1)

    def toggle_eventbar_move(self, event):
        button_pos = self.toggle_eventbar_button.mapToGlobal(event.position())
        handle_pos = self.handle_eventbar.mapToGlobal(self.handle_eventbar.rect().center())
        move_delta = button_pos.x() - handle_pos.x()
        new_x = handle_pos.x() + move_delta
        new_pos = QPoint(new_x, handle_pos.y())
        int_pos = int(new_pos.x())
        self.splitter.moveSplitter(int_pos, 2)

    def toggle_sidebar(self, left=True):
        sizes = self.splitter.sizes()
        left_size = sizes[0]
        center_size = sizes[1]
        right_size = sizes[2]
        if left_size != 0:
            sizes[0] = 0
            sizes[1] = left_size + sizes[1]
            sizes[2] = sizes[2]
        else:
            sizes[0] = self.min_width_sidebar_default
            sizes[1] = sizes[1] - self.min_width_sidebar_default
            sizes[2] = sizes[2]

        self.splitter.setSizes(sizes)

    def toggle_event_bar(self, left=True):
        sizes = self.splitter.sizes()
        left_size = sizes[0]
        center_size = sizes[1]
        right_size = sizes[2]

        # Định nghĩa minimum width cho event bar (phù hợp với event_type_row)
        min_width_eventbar = 300  # Đủ rộng để hiển thị event_type_row không bị cắt

        if right_size != 0:
            # Đang mở event bar -> đóng event bar
            sizes[0] = left_size  # Giữ nguyên sidebar
            sizes[1] = center_size + right_size  # Center nhận thêm không gian từ event bar
            sizes[2] = 0  # Đóng event bar
        else:
            # Đang đóng event bar -> mở event bar
            if center_size >= min_width_eventbar:
                sizes[0] = left_size  # Giữ nguyên sidebar
                sizes[1] = center_size - min_width_eventbar  # Center nhường không gian
                sizes[2] = min_width_eventbar  # Mở event bar
            else:
                # Nếu center quá nhỏ, giữ nguyên kích thước hiện tại
                return

        self.splitter.setSizes(sizes)

    def toggle_timeline(self, left=True):
        sizes = self.main_splitter.sizes()
        top_size = sizes[0]
        bottom_size = sizes[1]
        if bottom_size != 0:
            sizes[1] = 0
            sizes[0] = top_size + bottom_size
        else:
            sizes[0] = sizes[0] - self.min_height_timeline
            sizes[1] = self.min_height_timeline
        self.main_splitter.setSizes(sizes)

    def handle_splitter_moved(self, pos, index):
        sizes = self.splitter.sizes()

        # ✅ DEBUG LOG: Track splitter movements that might affect fullscreen items
        print(f"[SPLITTER_DEBUG] Splitter moved - pos: {pos}, index: {index}, sizes: {sizes}")

        # Check if any grid items are in fullscreen state
        if hasattr(self, 'center_view_widget') and hasattr(self.center_view_widget, 'grid_model'):
            grid_model = self.center_view_widget.grid_model
            if grid_model and hasattr(grid_model, 'listGridItems'):
                fullscreen_items = []
                for i in range(grid_model.listGridItems.rowCount()):
                    item = grid_model.listGridItems.data(grid_model.listGridItems.index(i, 0))
                    if item and hasattr(item, 'fullscreen') and item.fullscreen:
                        fullscreen_items.append(f"{item.itemType}({item.row},{item.col})")

                if fullscreen_items:
                    print(f"[SPLITTER_DEBUG] Fullscreen items detected during splitter move: {fullscreen_items}")

        # if index == 1:  # Handle 1
        #     if int(self.screen_available_width * 0.06) > self.splitter.sizes()[0]:
        #         sizes[0] = 0
        #         self.splitter.setSizes(sizes)
        #
        # if index == 2:  # Handle 2
        #     if int(self.screen_available_width * 0.06) > self.splitter.sizes()[2]:
        #         sizes[2] = 0
        #         self.splitter.setSizes(sizes)

    def load_tab_widget(self):
        self.add_tab_widget()

    def calculate_layout(self, desktop_screen_size=None):
        if desktop_screen_size is None:
            screen = QGuiApplication.primaryScreen()
            desktop_screen_size = screen.availableGeometry()
        self.screen_available_width = desktop_screen_size.width()
        self.screen_available_height = desktop_screen_size.height()
        menubar = self.screen_available_width * 0.0165
        margin = 40
        tab_bar = self.screen_available_height * 0.043
        height_of_button_toolbar = self.screen_available_height * 0.037
        percent_width_of_left_side_layout = 0.15
        percent_width_of_right_side_layout = 0.1 if Config.ENABLE_EVENT_BAR else 0.145
        percent_menu_bar = 0.05
        window_title = QApplication.style().pixelMetric(QStyle.PM_TitleBarHeight)
        self.width_left_side_layout = 0
        self.width_right_side_layout = 0

        self.width_left_side_layout = self.screen_available_width * \
            percent_width_of_left_side_layout
        if self.width_left_side_layout < self.screen_available_width * (percent_width_of_right_side_layout):
            self.width_left_side_layout = self.screen_available_width * \
                (percent_width_of_right_side_layout)
        self.width_right_side_layout = self.screen_available_width * \
            percent_width_of_right_side_layout

        if Config.ENABLE_EVENT_BAR:
            if self.width_right_side_layout < self.screen_available_width * percent_width_of_right_side_layout:
                self.width_right_side_layout = self.screen_available_width * percent_width_of_right_side_layout
        self.width_center_layout = self.screen_available_width * \
            ((1 - percent_width_of_left_side_layout - percent_width_of_right_side_layout - percent_menu_bar) if Config.ENABLE_EVENT_BAR else (1 - percent_width_of_left_side_layout - percent_menu_bar))
        self.grid_layout_width = self.width_center_layout
        self.grid_layout_height = self.width_center_layout * 9 / 16
        temp_grid_layout_height = self.screen_available_height - \
            height_of_button_toolbar - margin - tab_bar - window_title - 10
        if self.grid_layout_height > temp_grid_layout_height:
            self.grid_layout_height = temp_grid_layout_height
            self.grid_layout_width = self.grid_layout_height * 16 / 9

    def resize_layout(self):
        pass

    def on_show_event(self, event):
        pass

    def on_resize_event(self, event):
        self.frame_size = self.frameGeometry()

    def find_name_selected(self, tab_type=CommonEnum.TabType.VIRTUALWINDOW):
        name_list = []
        if tab_type == CommonEnum.TabType.VIRTUALWINDOW:
            # for tab_name, tab_model in tab_model_manager.tab_model_list.items():
            for id, gridModel in gridManager.data.items():
                if gridModel.get_property("type") == CommonEnum.TabType.VIRTUALWINDOW:
                    name_list.append(id)
            is_name_selected = False
            count = 0
            new_name = self.tr('Virtual Window ') + f'{count}'
            while not is_name_selected:
                new_name = self.tr('Virtual Window ') + f'{count}'
                if new_name not in name_list:
                    is_name_selected = True
                else:
                    count += 1
            return new_name
        elif tab_type == CommonEnum.TabType.SAVEDVIEW:
            for id, gridModel in gridManager.data.items():
                if gridModel.get_property("type") == CommonEnum.TabType.SAVEDVIEW:
                    name_list.append(gridModel.get_property('name'))
            is_name_selected = False
            count = 0
            new_name = self.tr('View ') + f'{count}'
            while not is_name_selected:
                new_name = self.tr('View ') + f'{count}'
                if new_name not in name_list:
                    is_name_selected = True
                else:
                    count += 1
            return new_name
        elif tab_type == CommonEnum.TabType.MAPVIEW:
            for id, gridModel in gridManager.data.items():
                if gridModel.get_property("type") == CommonEnum.TabType.MAPVIEW:
                    name_list.append(gridModel.get_property('name'))
            is_name_selected = False
            count = 0
            new_name = self.tr('Map ') + f'{count}'
            while not is_name_selected:
                new_name = self.tr('Map ') + f'{count}'
                if new_name not in name_list:
                    is_name_selected = True
                else:
                    count += 1
            return new_name
        else:
            for id, gridModel in gridManager.data.items():
                if gridModel.get_property("type") == CommonEnum.TabType.NORMAL:
                    name_list.append(gridModel.get_property('name'))
            is_name_selected = False
            count = 0
            new_name = self.tr('View ') + f'{count}'
            while not is_name_selected:
                new_name = self.tr('View ') + f'{count}'
                if new_name not in name_list:
                    is_name_selected = True
                else:
                    count += 1
            return new_name

    def create_tab_model(self,tab_type = CommonEnum.TabType.NORMAL):
        tab_name = self.find_name_selected(tab_type=tab_type)
        data = {
            "id": uuid.uuid4(),
            "name": tab_name,
            "type": tab_type,
            "isShow": True,
            "currentGrid": str({}),
            "listGridData": str({}),
            "listGridCustomData": "NewGrid",
            "direction": str({})
        }
        gridModel = GridModel(data=data)
        gridManager.addGridModel(data=gridModel)
        return gridModel
    
    def create_tracking_grid_model(self, camera_model_list={}, tab_name=None):
        listGridData = {}
        currentGrid = {
            "row": 3,
            "col": 3,
            "timestamp": 0,
            "name": f"ahihi"
        }

        for idx,camera in camera_model_list.items():
            if isinstance(camera, CameraModel):
                logger.info(f'camera = {camera.get_property("name")}-{idx}')
                if idx == (0,0):
                    listGridData[idx] = {
                        "type": CommonEnum.ItemType.CAMERA,
                        "position": idx,
                        "id": camera.get_property('id'),
                        "row": 0,
                        "col": 0,
                        'rows_cell': 2,
                        'cols_cell': 2,
                        "width": 1,
                        "height": 1
                    }
                elif idx == (1,2):
                    listGridData[idx] = {
                        "type": CommonEnum.ItemType.CAMERA,
                        "position": idx,
                        "id": camera.get_property('id'),
                        "row": 1,
                        "col": 2,
                        'rows_cell': 1,
                        'cols_cell': 1,
                        "width": 1,
                        "height": 1
                    }
                elif idx == (2,0):
                    listGridData[idx] = {
                        "type": CommonEnum.ItemType.CAMERA,
                        "position": idx,
                        "id": camera.get_property('id'),
                        "row": 2,
                        "col": 0,
                        'rows_cell': 1,
                        'cols_cell': 1,
                        "width": 1,
                        "height": 1
                    }
                elif idx == (2,1):
                    listGridData[idx] = {
                        "type": CommonEnum.ItemType.CAMERA,
                        "position": idx,
                        "id": camera.get_property('id'),
                        "row": 2,
                        "col": 1,
                        'rows_cell': 1,
                        'cols_cell': 1,
                        "width": 1,
                        "height": 1
                    }
                elif idx == (2,2):
                    listGridData[idx] = {
                        "type": CommonEnum.ItemType.CAMERA,
                        "position": idx,
                        "id": camera.get_property('id'),
                        "row": 2,
                        "col": 2,
                        'rows_cell': 1,
                        'cols_cell': 1,
                        "width": 1,
                        "height": 1
                    }
            elif isinstance(camera, EventModel):
                if idx == (0,2):
                    listGridData[idx] = {
                        "type": CommonEnum.ItemType.EVENT,
                        "position": idx,
                        "id": camera.id,
                        "row": 0,
                        "col": 2,
                        'rows_cell': 1,
                        'cols_cell': 1,
                        "width": 1,
                        "height": 1
                    }
        data = {
            "id": uuid.uuid4(),
            "name": tab_name,
            "type": CommonEnum.TabType.TRACKINGVIEW,
            "isShow": True,
            "currentGrid": str(currentGrid),
            "listGridData": str(listGridData),
            "listGridCustomData": "NewGrid",
            "direction": str({})
        }
        gridModel = GridModel(data=data)
        return gridModel

    def add_tab_widget(self, gridModel = None):
        # grid_item_selected.clear()
        if gridModel is None:
            gridModel = self.create_tab_model()
        ###########################################
        camera_grid_widget = CameraGridWidget(parent=self,gridModel = gridModel)
        self.center_stacked_widget.addWidget(camera_grid_widget)
        logger.info(f'add_tab_widget done')
        self.center_stacked_widget.setCurrentWidget(camera_grid_widget)

        self.new_custom_tab_widget.addTabWidget(gridModel = gridModel)
        logger.info(f'add_tab_widget done1')
        return camera_grid_widget

    def handle_color_preview(self, camera_id, camera_name):
        pass
        # widget = grid_item_selected.data['widget']
        # if hasattr(widget, 'grid_item_unclicked'):
        #     widget.grid_item_unclicked()
        # current_widget = self.center_stacked_widget.currentWidget()
        # current_tab_key = main_controller.current_tab
        # if grid_item_selected.is_tab_index(current_tab_key) and grid_item_selected.data['tab_index'] is not None:
        #     if camera_id == grid_item_selected.data['camera_id']:
        #         return
        # if isinstance(current_widget, CameraGridWidget):
        #     check = False
        #     camera_id_found = False
        #     # Tạm check ko lỗi để chạy ghép refactor phần grid manager
        #     # Check if current_widget has gridModel and it has the required data structure
        #     if hasattr(current_widget, 'gridModel') and current_widget.gridModel and hasattr(current_widget.gridModel, 'data') and hasattr(current_widget.gridModel.data, 'listGridData'):
        #         for index, grid_item in current_widget.gridModel.data.listGridData.items():
        #             if not camera_id_found and isinstance(grid_item.model,CameraModel) and camera_id == grid_item.model.get_property('id'):
        #                 camera_id_found = True
        #                 grid_item_selected.set_data(tab_index=current_tab_key,screen='Main',camera_id=camera_id,widget=grid_item.widget)
        #                 grid_item.widget.stack_item.grid_item_clicked(main_controller.current_tab)
        #                 break

    def stop_live_handle(self, data):
        current_widget = self.center_stacked_widget.currentWidget()
        if hasattr(current_widget, 'gridModel') and current_widget.gridModel:
            gridModel = current_widget.gridModel
        else:
            return
        if gridModel is not None:
            camera_model: GroupModel = camera_model_manager.get_camera_model(name=data)
            if camera_model is not None and hasattr(gridModel, 'data') and hasattr(gridModel.data, 'listGridData'):
                for index, grid_item in gridModel.data.listGridData.items():
                    if isinstance(grid_item.model, CameraModel) and grid_item.model.get_property('id') in camera_model.get_property('id'):
                        # Note: gridModel might not have remove_grid_item_signal, need to check implementation
                        if hasattr(gridModel, 'remove_grid_item_signal'):
                            gridModel.remove_grid_item_signal.emit(index)
        main_controller.gc_collect(current_widget)

    def stop_live_group_handle(self, data):
        current_widget: CameraGridWidget = self.center_stacked_widget.currentWidget()
        if hasattr(current_widget, 'gridModel') and current_widget.gridModel:
            gridModel = current_widget.gridModel
        else:
            return
        group_name = data
        if gridModel is not None and hasattr(gridModel, 'data') and hasattr(gridModel.data, 'listGridData'):
            group_model: GroupModel = group_model_manager.get_group_model(name=group_name)
            if group_model is not None:
                for index, grid_item in gridModel.data.listGridData.items():
                    if isinstance(grid_item.model, CameraModel) and grid_item.model.get_property('id') in group_model.get_property("cameraIds"):
                        if hasattr(gridModel, 'remove_grid_item_signal'):
                            gridModel.remove_grid_item_signal.emit(index)
        main_controller.gc_collect(current_widget)

    def event_open_new_tab(self,data):
        key, id = data
        widget = None
        if key == 'New View':
            widget = self.add_tab_widget()

        elif key == 'New Saved View':
            tab_name = self.main_treeview_widget.find_name_selected(CommonEnum.TabType.SAVEDVIEW)
            list_saved_view_item = self.main_treeview_widget.get_item('List Saved View', tree_type=TreeType.List_Saved_View)
            new_item_saved_view = self.main_treeview_widget.add_saved_view_triggered(list_saved_view_item)
            widget = self.main_treeview_widget.new_tab_savedview_triggered(new_item_saved_view)

        elif key == 'New Virtual Window':
            tab_name = self.main_treeview_widget.find_name_selected(CommonEnum.TabType.VIRTUALWINDOW)
            list_virtual_window_item = self.main_treeview_widget.get_item('List Virtual Window',
                                                                      tree_type=TreeType.List_Virtual_Window)
            new_item = self.main_treeview_widget.new_virtual_window_triggered(list_virtual_window_item)
            widget = self.main_treeview_widget.auto_open_virtual_window(new_item)

    def open_item_in_savedview(self,tab_type = CommonEnum.TabType.NORMAL,server_ip = None,model = None):
        controller:Controller = controller_manager.get_controller(server_ip=server_ip)
        server = self.main_treeview_widget.get_server(server_ip = server_ip)
        if tab_type == CommonEnum.TabType.SAVEDVIEW:
            tab_name = self.find_name_selected(CommonEnum.TabType.SAVEDVIEW)
            data = {
                # "id": None,
                "name": tab_name,
                "type": CommonEnum.TabType.SAVEDVIEW,
                "isShow": False,
                "currentGrid": "{}",
                "listGridData": "{}",
                "listGridCustomData": "NewGrid",
                "direction": str({'id': str(uuid.uuid4())})
            }
            def callback(data):
                if data is not None:
                    list_savedview_item = self.main_treeview_widget.get_item('Saved View List', tree_type=TreeType.List_Saved_View,server_ip=server_ip)
                    data["server_ip"] = server_ip
                    data["type"] = int(data["type"])
                    gridModel = GridModel(data=data)
                    gridManager.addGridModel(data=gridModel)
                    self.main_treeview_widget.add_data(name=gridModel.get_property("name",''), tab_type=TreeType.Saved_View_Item, server=server,model=gridModel)
                    root_item = self.main_treeview_widget.add_item(item=list_savedview_item, name=gridModel.get_property("name",''), tree_type=TreeType.Saved_View_Item,server_ip = server.name,model=gridModel)
                    root_item.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'open_all_virtual')))
                    self.add_tab_widget(gridModel = gridModel)
                    if isinstance(model,GroupModel):
                        gridModel.openTreeItem.emit(model.data,"Group")
                        gridModel.shortcut_activated()
                    elif isinstance(model,CameraModel):
                        gridModel.openTreeItem.emit(model.data,"Camera")
                        gridModel.shortcut_activated()
            subThread = SubThread(parent=self, target=controller.create_tabmodel, args=(data,),callback=callback)
            subThread.start()
        elif tab_type == CommonEnum.TabType.VIRTUALWINDOW:
            tab_name = self.find_name_selected(CommonEnum.TabType.VIRTUALWINDOW)
            data = {
                # "id": None,
                "name": tab_name,
                "type": CommonEnum.TabType.VIRTUALWINDOW,
                "isShow": False,
                "currentGrid": "{}",
                "listGridData": "{}",
                "listGridCustomData": "NewGrid",
                "direction": str({'id': str(uuid.uuid4())})
            }
            def callback(data):
                if data is not None:
                    list_virtual_item = self.main_treeview_widget.get_item('Virtual Window List', tree_type=TreeType.List_Virtual_Window,server_ip=server_ip)
                    data["server_ip"] = server_ip
                    data["type"] = int(data["type"])
                    gridModel = GridModel(data=data)
                    gridManager.addGridModel(data=gridModel)
                    self.main_treeview_widget.add_data(name=gridModel.get_property("name",''), tab_type=TreeType.Virtual_Window_Item, server=server,model=gridModel)
                    root_item = self.main_treeview_widget.add_item(item=list_virtual_item, name=gridModel.get_property("name",''), tree_type=TreeType.Virtual_Window_Item,server_ip = server.name,model=gridModel)
                    root_item.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'open_all_virtual')))
                    # tạm thời mở virtual window ở màn chính, sau sẽ có cơ chế check để auto mở sang màn hình khác màn hình đang mở app chính
                    self.main_treeview_widget.open_to_window_triggered(0,root_item)
                    if isinstance(model,GroupModel):
                        gridModel.openTreeItem.emit(model.data,"Group")
                        gridModel.shortcut_activated()
                    elif isinstance(model,CameraModel):
                        gridModel.openTreeItem.emit(model.data,"Camera")
                        gridModel.shortcut_activated()
                
            subThread = SubThread(parent=self, target=controller.create_tabmodel, args=(data,),callback=callback)
            subThread.start() 

    def open_items_in_savedview(self,tab_type = CommonEnum.TabType.NORMAL, list_camera_selection = None):
        server_ip = None
        if len(list_camera_selection) > 0:
            server_ip = list_camera_selection[0].get_property("server_ip", None)
        if len(list_camera_selection) > 0:
            rawData = {}
            for cameraModel in list_camera_selection:
                rawData[cameraModel.id] = {"name":cameraModel.name, "type": "Camera"}
        if server_ip is not None:
            controller:Controller = controller_manager.get_controller(server_ip=server_ip)
            server = self.main_treeview_widget.get_server(server_ip = server_ip)
            if tab_type == CommonEnum.TabType.SAVEDVIEW:
                tab_name = self.find_name_selected(tab_type)
                data = {
                    # "id": None,
                    "name": tab_name,
                    "type": tab_type,
                    "isShow": False,
                    "currentGrid": "{}",
                    "listGridData": "{}",
                    "listGridCustomData": "NewGrid",
                    "direction": str({'id': str(uuid.uuid4())})
                }
                def callback(data):
                    if data is not None:
                        treeview_item = self.main_treeview_widget.get_item('Saved View List', tree_type=TreeType.List_Saved_View,server_ip=server_ip)
                        data["server_ip"] = server_ip
                        data["type"] = int(data["type"])
                        gridModel = GridModel(data=data)
                        gridManager.addGridModel(data=gridModel)
                        self.main_treeview_widget.add_data(name=gridModel.get_property("name",''), tab_type=TreeType.Saved_View_Item, server=server,model=gridModel)
                        root_item = self.main_treeview_widget.add_item(item=treeview_item, name=gridModel.get_property("name",''), tree_type=TreeType.Saved_View_Item,server_ip = server.name,model=gridModel)
                        root_item.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'open_all_virtual')))
                        self.add_tab_widget(gridModel = gridModel)
                        gridModel.processHandleDrop(rawData,"multi-selection")
                        gridModel.shortcut_activated()
                subThread = SubThread(parent=self, target=controller.create_tabmodel, args=(data,),callback=callback)
                subThread.start()
            elif tab_type == CommonEnum.TabType.VIRTUALWINDOW:
                tab_name = self.find_name_selected(tab_type)
                data = {
                    # "id": None,
                    "name": tab_name,
                    "type": tab_type,
                    "isShow": False,
                    "currentGrid": "{}",
                    "listGridData": "{}",
                    "listGridCustomData": "NewGrid",
                    "direction": str({'id': str(uuid.uuid4())})
                }
                def callback(data):
                    if data is not None:
                        treeview_item = self.main_treeview_widget.get_item('Virtual Window List', tree_type=TreeType.List_Virtual_Window,server_ip=server_ip)
                        data["server_ip"] = server_ip
                        data["type"] = int(data["type"])
                        gridModel = GridModel(data=data)
                        gridManager.addGridModel(data=gridModel)
                        self.main_treeview_widget.add_data(name=gridModel.get_property("name",''), tab_type=TreeType.Virtual_Window_Item, server=server,model=gridModel)
                        root_item = self.main_treeview_widget.add_item(item=treeview_item, name=gridModel.get_property("name",''), tree_type=TreeType.Virtual_Window_Item,server_ip = server.name,model=gridModel)
                        root_item.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'open_all_virtual')))
                        # tạm thời mở virtual window ở màn chính, sau sẽ có cơ chế check để auto mở sang màn hình khác màn hình đang mở app chính
                        self.main_treeview_widget.open_to_window_triggered(0,root_item)
                        gridModel.processHandleDrop(rawData,"multi-selection")
                        gridModel.shortcut_activated()
                subThread = SubThread(parent=self, target=controller.create_tabmodel, args=(data,),callback=callback)
                subThread.start()

    def open_items_in_tab(self, data):
        logger.info(f'open_items_in_tab = {data}')
        model, gridModel, row, col, item_data, list_camera_selection = data
        if item_data == TreeType.Camera:
            if gridModel == 'New View':
                widget = self.add_tab_widget()
                # widget.gridModel.processHandleDrop(model.data,"Camera")
                widget.gridModel.openTreeItem.emit(model.data,"Camera")

            elif gridModel == 'New Saved View':
                self.open_item_in_savedview(tab_type=CommonEnum.TabType.SAVEDVIEW,server_ip = model.get_property('server_ip'),model=model)

            elif gridModel == 'New Virtual Window':
                self.open_item_in_savedview(tab_type=CommonEnum.TabType.VIRTUALWINDOW,server_ip = model.get_property('server_ip'),model=model)

            else:
                pass

        elif item_data == TreeType.Group:
            if gridModel == 'New View':
                widget = self.add_tab_widget()
                widget.gridModel.openTreeItem.emit(model.data,"Group")

            elif gridModel == 'New Saved View':
                self.open_item_in_savedview(tab_type=CommonEnum.TabType.SAVEDVIEW,server_ip = model.get_property('server_ip'),model=model)
                                          
            elif gridModel == 'New Virtual Window':
                self.open_item_in_savedview(tab_type=CommonEnum.TabType.VIRTUALWINDOW,server_ip = model.get_property('server_ip'),model=model)
                     
            else:
                widget = self.new_custom_tab_widget.getWidgetByGridModel(gridModel)
                if widget is not None:
                    gridModel.openTreeItem.emit(model.data,"Group")

        elif item_data == TreeType.Multi_Select_Item:
            if gridModel == 'New View':
                widget = self.add_tab_widget()
                if len(list_camera_selection) > 0:
                    rawData = {}
                    for cameraModel in list_camera_selection:
                        rawData[cameraModel.id] = {"name":cameraModel.name, "type": "Camera"}
                    widget.gridModel.processHandleDrop(rawData,"multi-selection")
            elif gridModel == 'New Saved View':
                self.open_items_in_savedview(tab_type=CommonEnum.TabType.SAVEDVIEW,list_camera_selection=list_camera_selection)
            elif gridModel == 'New Virtual Window':
                self.open_items_in_savedview(tab_type=CommonEnum.TabType.VIRTUALWINDOW,list_camera_selection=list_camera_selection)
            else:
                pass

    def open_floor_in_tab_signal(self, data):
        model, tab_name, row, col, item_data, server_ip = data
        if item_data == TreeType.FloorItem:
            widget = self.new_custom_tab_widget.getWidgetByName(tab_name)
            if widget:
                widget: CameraGridWidget
                value = (model, row, col, widget.frame_width,
                            widget.frame_height, server_ip)
                widget.open_floor_in_position(value)


    def open_map_in_tab(self, data):
        model, tab_name, row, col, item_data, list_camera_selection, server_ip = data
        if item_data == TreeType.List_Map:
            widget = self.new_custom_tab_widget.getWidgetByName(tab_name)
            self.open_map_group_in_grid(widget=widget, model=model, row=row, col=col, server_ip=server_ip)

    def open_map_group_in_grid(self, widget=None, model=None, row=None, col=None, server_ip=None):
        if widget:
            widget: CameraGridWidget
            value = (model, row, col, widget.frame_width,
                        widget.frame_height, server_ip)
            widget.open_map_in_position(value)

    def open_camera_in_position(self, data):
        camera_name = data[0]
        tab_name = data[1]
        row_col = data[2]
        row = row_col[0]
        col = row_col[1]
        current_widget: CameraGridWidget = self.center_stacked_widget.currentWidget()
        value = (camera_name, row, col, current_widget.frame_width,
                 current_widget.frame_height)
        current_widget.open_in_position_camera(value)

    def edit_floor_signal(self, data):
        item, tab_type = data
        floor_model = item.item_model
        if floor_model is not None:
            # Kiểm tra xem tên tab và type có tồn tại chưa nếu chưa thì tạo tab mới
            gridModel_result: GridModel = gridManager.getGridModel(id=floor_model.id)
            if gridModel_result is not None:
                for index in range(self.new_custom_tab_widget.tab_widget.count()):
                    widget = self.new_custom_tab_widget.getWidgetByIndex(index)
                    if widget.gridModel == gridModel_result:
                        self.new_custom_tab_widget.tab_widget.setCurrentIndex(index)
                        return
            data = {
                "id": floor_model.id,
                "name": self.tr("Editing ") + item.text(),
                "server_ip": floor_model.serverIp,
                "type": tab_type,
                "isShow": False,
                "currentGrid": "{}",
                "listGridData": "{}",
                "listGridCustomData": "NewGrid",
                "direction": str({'id': str(uuid.uuid4())})
            }
            gridModel = GridModel(data=data)
            gridManager.addGridModel(data=gridModel)
            camera_grid_widget = self.add_tab_widget(gridModel=gridModel)

    def edit_map_handle(self, data):
        item, tab_type = data
        map_model = item.item_model
        if map_model is not None:
            # Kiểm tra xem tên tab và type có tồn tại chưa nếu chưa thì tạo tab mới
            gridModel_result: GridModel = gridManager.getGridModel(id=map_model.id)
            if gridModel_result is not None:
                for index in range(self.new_custom_tab_widget.tab_widget.count()):
                    widget = self.new_custom_tab_widget.getWidgetByIndex(index)
                    if widget.gridModel == gridModel_result:
                        self.new_custom_tab_widget.tab_widget.setCurrentIndex(index)
                        return

            data = {
                "id": map_model.id,
                "name": self.tr("Editing digital map"),
                "server_ip": map_model.serverIp,
                "type": tab_type,
                "isShow": False,
                "currentGrid": "{}",
                "listGridData": "{}",
                "listGridCustomData": "NewGrid",
                "direction": str({'id': str(uuid.uuid4())})
            }
            gridModel = GridModel(data=data)
            gridManager.addGridModel(data=gridModel)
            camera_grid_widget = self.add_tab_widget(gridModel=gridModel)

    def complete_fetching_data(self,data):
        controller:Controller = data
        self.main_treeview_widget.create_server(controller=controller)
        self.main_treeview_widget.update_treeview_data(server_ip=controller.server.data.server_ip)
        self.main_treeview_widget.put_filter_queue({'text':self.widget_search_title_bar.search_widget.search_bar.text(),'status':Status.Invalid})

    def default_timelinecontroller(self):
        if self.timeLineManager is not None:
            self.timeLineManager.timeLineController = self.timelinecontroller
            self.timelinecontroller.setIsTimeLine(False)
            self.timelinecontroller.setCameraName(self.tr('No camera selected'))

    def openCalendarDialog(self, data):
        if data.isCalendar:
            if self.calendar_dialog is not None:
                self.calendar_dialog.close()
                self.calendar_dialog = None
        else:
            self.calendar_dialog = CalendarDialog(parent=self,timeLineManager=self.timeLineManager)
            self.calendar_dialog.show()

    def add_event_signal(self, event_id):
        if screenModelManager.isTracking:
            # self.event_processor.event_queue.put(event_id)
            threadPoolManager.run("CameraScreen",target=self.process_event,callback=self.callback_event,args=(event_id,))

    def find_value_in_dict(self, target_dict, value):
        for key, items in target_dict.items():
            for item in items:
                if value == item[1]:
                    return key, item[0], True
        return None, None, False

    def handle_alert(self, tab_model, event, next_camera_ids, close_alert):
        for index, grid_item in tab_model.data.listGridData.items():
            if grid_item.virtual_widget is not None and isinstance(grid_item.virtual_widget, CameraWidget):
                if close_alert:
                    grid_item.virtual_widget.warning_alert_widget.close_alert_widget()
                if event.get_prperty("cameraId") == grid_item.model.get_property('id'):
                    grid_item.virtual_widget.add_alert_to_queue(event)
                if grid_item.model.get_property('id') in next_camera_ids:
                    grid_item.virtual_widget.prepare_show_next_tracking_animation()

    def diff_camera_list(self, new_list_camera_tracing={}, gridModel:GridModel=None):
        listGridItems = gridModel.listGridItems._items
        for idx, model in new_list_camera_tracing.items():
            for index, cameraGridItem in listGridItems.items():
                if isinstance(cameraGridItem,CameraGridItem) and cameraGridItem.cameraModel == model and idx != index:
                    logger.info(f'diff_camera_list = {idx}-{model.get_property("name")}')
                    gridModel.listGridItems.swapItemsRowCol(index[0],index[1], idx[0],idx[1])
                    # break
            # break
        for idx, model in new_list_camera_tracing.items():
            if isinstance(model,CameraModel):
                isCheck = False
                for index, cameraGridItem in listGridItems.items():
                    if isinstance(cameraGridItem,CameraGridItem) and cameraGridItem.cameraModel == model:
                        isCheck = True
                if not isCheck:
                    temp = listGridItems[idx]
                    temp.cameraModel = model
            elif isinstance(model,EventModel):
                eventGridItem = listGridItems[idx]
                eventGridItem.eventModel = model    


    def item_alert_in_treeview(self, camera_name):
        self.main_treeview_widget.change_item_color(camera_name, QColor(236, 122, 26))
        QTimer.singleShot(5000, lambda: self.main_treeview_widget.change_item_color(camera_name, QColor(f"{main_controller.get_theme_attribute('Color', 'main_background')}")))

    def stop_app(self):
        if Config.ENABLE_WARNING_ALERT_CAMERA:
            self.run_alert_processor = False

        if Config.ENABLE_EVENT_BAR:
            self.event_bar.event_list_view.thread_pool.shutdown(
                wait=True, cancel_futures=True)

    def callback_tab_changed(self, idx):
        self.center_stacked_widget.setCurrentIndex(idx)
        current_widget: CameraGridWidget = self.center_stacked_widget.currentWidget()
        self.camera_bottom_toolbar.set_camera_grid_widget(current_widget)
        # if current_widget.tab_model is not None:
        #     list_custom_grid_model = []
            # if current_widget.tab_model.data.index == idx:
            #     current_list_data_custom_grid = current_widget.tab_model.data.listGridCustomData
            #     current_grid_model = current_widget.tab_model.data.currentGrid
            #     if current_list_data_custom_grid is not None and current_grid_model is not None:
            #         for grid_data in current_list_data_custom_grid:
            #             model = ItemGridModel.from_dict(grid_data)
            #             list_custom_grid_model.append(model)
            #         current_model = ItemGridModel.from_dict(current_grid_model)
            #         current_widget.camera_bottom_toolbar.list_custom_grid_model = list_custom_grid_model
            #         current_widget.camera_bottom_toolbar.update_ui_grid_menu(list_custom_grid_model, current_model)

    def search_items(self,text):
        self.main_treeview_widget.put_filter_queue({'text':text,'status':Status.Invalid})

    def change_mode_trigger(self,data):
        self.filter_text.show()
        if data == "All":
            self.filter_text.hide()
            self.main_treeview_widget.filter_mode_status = TreeType.Invalid
            self.main_treeview_widget.put_filter_queue({'text':self.widget_search_title_bar.search_widget.search_bar.text(),'status':Status.Change_Filter_Mode})
        elif data == "Server":
            self.filter_text.label.setText(data)
            self.main_treeview_widget.filter_mode_status = TreeType.Server
            self.main_treeview_widget.put_filter_queue({'text':self.widget_search_title_bar.search_widget.search_bar.text(),'status':Status.Change_Filter_Mode})
        elif data == "Camera Group":
            self.filter_text.label.setText(data)
            self.main_treeview_widget.filter_mode_status = TreeType.List_Camera
            self.main_treeview_widget.put_filter_queue({'text':self.widget_search_title_bar.search_widget.search_bar.text(),'status':Status.Change_Filter_Mode})
        elif data == "Virtual Window":
            self.filter_text.label.setText(data)
            self.main_treeview_widget.filter_mode_status = TreeType.List_Virtual_Window
            self.main_treeview_widget.put_filter_queue({'text':self.widget_search_title_bar.search_widget.search_bar.text(),'status':Status.Change_Filter_Mode})
        elif data == "Saved View":
            self.filter_text.label.setText(data)
            self.main_treeview_widget.filter_mode_status = TreeType.List_Saved_View
            self.main_treeview_widget.put_filter_queue({'text':self.widget_search_title_bar.search_widget.search_bar.text(),'status':Status.Change_Filter_Mode})
        elif data == "Map":
            self.filter_text.label.setText(data)
            self.main_treeview_widget.filter_mode_status = TreeType.List_Map
            self.main_treeview_widget.put_filter_queue({'text':self.widget_search_title_bar.search_widget.search_bar.text(),'status':Status.Change_Filter_Mode})
        else:
            pass

    def icon_clicked(self):
        self.filter_text.hide()
        self.main_treeview_widget.filter_mode_status = TreeType.Server
        self.main_treeview_widget.put_filter_queue(
            {'text': self.widget_search_title_bar.search_widget.search_bar.text(), 'status': Status.Change_Filter_Mode})

    def eventFilter(self, obj, event):
        if obj == self:
            pass
        if obj.objectName() == "toggle_sidebar_button":
            if self.splitter is not None and self.splitter.widget(0) is not None:
                is_sidebar_visible = self.splitter.sizes()[0]
                if isinstance(obj, ButtonStatusSidebar):
                    if event.type() == QEvent.Type.Enter:
                        if is_sidebar_visible == 0:
                            self.toggle_sidebar_button.setToolTip(self.tr("Open the left sidebar"))
                            self.toggle_sidebar_button.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'icon_sidebar_big_right')))
                        else:
                            self.toggle_sidebar_button.setToolTip(self.tr("Close the left sidebar"))
                            self.toggle_sidebar_button.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'icon_sidebar_big_left')))
                    elif event.type() == QEvent.Type.Leave:
                        self.toggle_sidebar_button.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'icon_status_sidebar')))

        if obj.objectName() == "toggle_eventbar_button":
            if self.splitter is not None and self.splitter.widget(2) is not None:
                is_event_bar_visible = self.splitter.sizes()[2]
                if isinstance(obj, ButtonStatusSidebar):
                    if event.type() == QEvent.Type.Enter:
                        if is_event_bar_visible == 0:
                            self.toggle_eventbar_button.setToolTip(self.tr("Open the event bar"))
                            self.toggle_eventbar_button.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'icon_sidebar_big_left')))
                        else:
                            self.toggle_eventbar_button.setToolTip(self.tr("Close the event bar"))
                            self.toggle_eventbar_button.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'icon_sidebar_big_right')))
                    elif event.type() == QEvent.Type.Leave:
                        self.toggle_eventbar_button.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'icon_status_sidebar')))

        return super().eventFilter(obj, event)

    def restyle_camera_screen(self):
        self.main_treeview_widget.restyle_main_treeview_widget()
        self.new_custom_tab_widget.restyle_custom_tab_widget()
        self.widget_button_system.set_dynamic_stylesheet()
        self.camera_bottom_toolbar.restyle_camera_bottom_toolbar()
        self.center_view_widget.setStyleSheet(f'''
                 QWidget#center_view_widget{{
                 border-top: 1px solid {main_controller.get_theme_attribute('Color','common_border')};
                 }}
             ''')
        self.splitter.setStyleSheet(
            f"""QSplitter::handle {{ 
            background-color: {main_controller.get_theme_attribute('Color', 'main_background_splitter')}; 
            border-top: 1px solid {main_controller.get_theme_attribute('Color','common_border')}
            }}""")

        self.main_splitter.setStyleSheet(
            f"""QSplitter::handle {{ 
                    background-color: {main_controller.get_theme_attribute('Color', 'main_background_splitter')}; 
                    border-top: 1px solid {main_controller.get_theme_attribute('Color', 'common_border')}
                    }}""")
        self.toggle_timeline_button.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'icon_status_timeline')))
        self.toggle_sidebar_button.setIcon(QIcon(main_controller.get_theme_attribute('Image','icon_status_sidebar')))
        self.toggle_eventbar_button.setIcon(QIcon(main_controller.get_theme_attribute('Image','icon_status_sidebar')))

        if Config.ENABLE_EVENT_BAR:
            self.event_bar.restyle_even_bar()


        # CAMERA GRID WIDGET - NEED REFACTOR
        # list_widget = self.new_custom_tab_widget.getAllWidget()
        # for widget in list_widget:
        #     widget: CameraGridWidget
        #     widget.restyle_camera_grid_widget()

        self.widget_search_title_bar.set_dynamic_stylesheet()
        self.widget_title_bar.set_dynamic_stylesheet()
        self.side_bar_widget.setStyleSheet(f'''
            QWidget#side_bar_widget {{
                border-left: None; 
                border-top: 1px solid {main_controller.get_theme_attribute('Color','common_border')};
                border-bottom: None;
                border-right: 1px solid {main_controller.get_theme_attribute('Color','common_border')};
            }}
        ''')
        self.filter_text.set_dynamic_stylesheet()

        # self.timelinecontroller.setTheme("dark" if main_controller.current_theme == Theme.DARK else "light")


    def retranslate_camera_screen(self):
        self.main_treeview_widget.safe_retranslate_ui()
        self.widget_search_title_bar.retransalate_ui()
        self.widget_search_title_bar.search_widget.retranslateUi_searchbar()
        if Config.ENABLE_EVENT_BAR:
            self.event_bar.retranslateUi()
        list_widget = self.new_custom_tab_widget.getAllWidget()
        # for widget in list_widget:
        #     widget: CameraGridWidget
        #     widget.retranslateUi()

    def closeEvent(self, event):
        logger.info(f'closeEvent CameraScreen')
        self.event_processor.stop()
        self.event_processor.event_queue.join()
        super().closeEvent(event)
