from abc import abstractmethod
from http import HTTPStatus
import json
import time
from typing import Callable, Generic, List, Optional, TypeVar
import websocket
import threading
from websocket._exceptions import WebSocketBadStatusException
from .event_type import EventType
from .message_processor import MessageProcessor
from src.common.controller.controller_manager import Controller, controller_manager
from src.common.qml.models.map_controller import map_manager, MapModel
from PySide6.QtCore import QObject, Signal
import queue
import logging

logger = logging.getLogger(__name__)

THandler = TypeVar('THandler', bound=object)

class WebsocketClient(Generic[THandler]):

    ws: websocket.WebSocketApp
    url: str
    event_callback: Optional[Callable[[dict], None]]
    header: dict
    handlers: List[THandler]
    server_ip: str
    _auth_error: bool  # Flag để track authentication error

    def __init__(self, url: str, header: Optional[dict] = None,
                 server_ip: Optional[str] = None):
        self.url = url

        self.header = header or {}
        self.server_ip = server_ip
        self.handlers = []
        self._auth_error = False  # Khởi tạo flag

        self.ws = websocket.WebSocketApp(
            url=self.url,
            on_open=self.on_open,
            on_message=self.on_message,
            on_error=self.on_error,
            on_close=self.on_close,
            header=self.header
        )
        self.messageProcessor = MessageProcessor(server_ip=server_ip)
        
    def connect_background(self):
        self.thread = threading.Thread(target=self.connect)
        self.thread.daemon = True
        self.thread.start()

    def connect(self):
        self.ws.run_forever()

    def set_url(self, url: str):
        self.url = url
        self.ws.url = url

    def subscribe(self, handler: THandler):
        self.handlers.append(handler)

    def send(self, data):
        self.ws.send(data)

    def close(self):
        self.ws.close()

    def reset_auth_error(self):
        """Reset authentication error flag - gọi sau khi login thành công"""
        self._auth_error = False

    def on_open(self, ws):
        data = {"message": "You are connected"}
        event = EventType.connection_established
        message_json = {'event': event, 'data': data}
        self.__on_websocket_event(message_json)
        logger.info("Connected")

    def on_message(self, ws, message):
        try:
            message_json = json.loads(message)
            print(f"[WebSocket] Received event: {message_json}")  # Log event
            # xử lý tính toán dữ liệu lâu
            self.messageProcessor.message_queue.put(message_json)
            # self.__on_websocket_event(message_json)
        except Exception as e:
            logger.exception("Error in on_message", exc_info=True)

    def on_error(self, ws, error: Exception):
        message = f"WebSocket error: {str(error)}"
        data = {"message": message, "error": str(error)}
        event = EventType.connection_error
        message_json = {'event': event, 'data': data}
        self.__on_websocket_event(message_json)

        if isinstance(error, WebSocketBadStatusException):
            if error.status_code == HTTPStatus.UNAUTHORIZED:
                # Không tự động refresh token nữa, yêu cầu login lại
                self._handle_unauthorized_error()

    def _handle_unauthorized_error(self):
        """Xử lý lỗi UNAUTHORIZED - yêu cầu người dùng login lại"""
        logger.warning("Token expired or invalid. User needs to login again.")

        # Set flag để ngăn auto reconnect
        self._auth_error = True

        # Emit event để thông báo cần login lại
        data = {
            "message": "Your session has expired. Please login again.",
            "action_required": "LOGIN_REQUIRED",
            "server_ip": self.server_ip
        }
        event = EventType.authentication_required
        message_json = {'event': event, 'data': data}
        self.__on_websocket_event(message_json)

        # Đóng websocket connection
        self.close()
                    
    # def refresh_token(self) -> bool:
    #     """
    #     DEPRECATED: Không tự động refresh token nữa.
    #     Thay vào đó yêu cầu người dùng login lại khi token hết hạn.
    #     """
    #     try:
    #         controller: Controller = controller_manager.get_controller(server_ip=self.server_ip)
    #         if not controller:
    #             return False

    #         access_token = controller.refresh_access_token()
    #         if access_token:
    #             self.header['Authorization'] = f"Bearer {access_token}"
    #             map_model: MapModel = map_manager.get_map_model(serverIp=self.server_ip)
    #             if map_model:
    #                 map_model.accessTokenChanged.emit()
    #             return True
    #         return False
    #     except Exception as e:
    #         logger.error(f"Token refresh error: {e}")
    #         return False

    def on_close(self, ws, close_status_code, close_msg):
        message = f"close_status_code = {close_status_code} close_msg = {close_msg}"
        data = {"message": message}
        event = EventType.connection_lost
        message_json = {'event': event, 'data': data}
        self.__on_websocket_event(message_json)

        # Không reconnect nếu đóng do authentication error
        if self._auth_error:
            logger.info(f"Connection closed due to authentication error. Not reconnecting.")
            return

        logger.info(f"Trying reconnect {close_status_code} {close_msg}")
        time.sleep(1)
        self.connect_background()

    def __on_websocket_event(self, message_json: dict):
        try:
            event = message_json['event']
            for handler in self.handlers:
                if event in handler.event_types:
                    handler.enqueue(message=message_json)
        except Exception:
            logger.exception("on_websocket_event exception error", exc_info=True)

