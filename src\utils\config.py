# this class config constants values
class Config:
    # SKIP LOGIN SCREEN and OFFLINE MODE
    SERVER_AVAILABLE = True

    # Ratio
    RATIO_CAMERA = 16 / 9

    BUILD_RELEASE = False

    # Debug flag
    DEBUG = False

    ENABLE_FORCE_SERVER_CONFIG = False

    SERVER_IP_ADDRESS_DEFAULT = '**********'

    SERVER_IP_ADDRESS_DJANGO_DEFAULT = '**********'

    SERVER_VMS_PORT_DEFAULT = 48080

    SERVER_EVENT_PORT_DEFAULT = 18080

    SERVER_EVENT_PATH_DEFAULT = '/ws/vms-server/'
    SERVER_EVENT_PATH_DEFAULT_1 = '/ws/'

    CAPTCHA_URL_PATH = '/user-service/captcha/generate'

    CYGATE_API_URL = 'dev-api.cygate.vn'

    EMS_API_URL = 'api.gpstech.vn'

    EMS_API_URL_BACKUP = 'ems.gpstech.vn'

    ENABLE_FORCE_LOGIN = True

    # Camera
    USE_START_CAMERA_THREAD = True

    # Use List Camera in JSON file instead of from QSettings
    ENABLE_LOCAL_CAMERA_JSON_FILE = False

    # LOG_FILE_PATH
    LOG_FILE_PATH = "log.txt"

    ENABLE_PLAYBACK_SCREEN = True

    ENABLE_DEVICE_SCREEN = True

    ENABLE_SETTING_SCREEN = True

    ENABLE_CAMERA_SCREEN = True

    ENABLE_RECORD_BUTTON = False

    ENABLE_CAPTURE_BUTTON = False

    ENABLE_SPEAKER_BUTTON = False

    ENABLE_MICROPHONE_BUTTON = False

    ENABLE_ASPECT_RATIO_BUTTON = False

    ENABLE_CLOSE_STREAMING_BUTTON = True

    ENABLE_FULL_SCREEN_BUTTON = False

    ENABLE_GRID_BUTTON = True

    ENABLE_STREAM_FLOW_BUTTON = True

    ENABLE_PTZ_MODULE = True
    # Enable SSO and Forgot password
    ENABLE_FORGOT_PASSWORD = False
    ENABLE_SSO_LOGIN = False

    ENABLE_AI_CONFIG_IMAGE_DIAGONAL_LENGTH = False
    # Enable Change Server
    ENABLE_CHANGE_SERVER = True

    USE_FFMPEG_STREAM = False

    USE_OPENCV_STREAM = False  # True: use opencv stream, False: use pyav stream

    USE_OPENGL_RENDER = False  # True: use opengl render, False: use qimage render

    ENABLE_IMAGE_DIAGONAL = False

    # Setting Screen
    ENABLE_VOICE = False

    ENABLE_MULTI_ALERT_CHANNEL = False

    ENABLE_EVENT_BAR = True

    ENABLE_WARNING_ALERT_CAMERA = True

    ENABLE_WEBSOCKET_EVENT = True

    ENABLE_WEBSOCKET_VMS = True
    
    ENABLE_WEBSOCKET_SAVEDVIEW = True
    
    HIGHER_GRID_NUMBER_USE_SUB_STREAM = 9

    FRAME_WIDTH_DEFAULT_SERVER = 1920

    FRAME_HEIGHT_DEFAULT_SERVER = 1080
    # Ptz
    ENABLE_CROP_FRAME = False

    ENABLE_MAP = True

    ENABLE_DOWNLOAD_DATA = False

    ENABLE_MULTI_EDGE_ZONES = False

    ENABLE_CHECK_ONVIF = True

    ACCESS_CONTROL_HUMAN = True

    ENABLE_ARC_CAMERA_MAP_ITEM = True

    SHOW_FPS_CAMERA_DEBUG = False

    SHOW_ID_CAMERA_DEBUG = True

    STREAM_AI_IN_TRACKING_WINDOW = True

    CONFIG_TEST_WARNING = False

    USER_ROLE_STATUS_ADDED = 2571

    USER_ROLE_COMBOBOX = 2570

    USER_ROLE_STATUS = 2580

    ROLE_DESCRIPTION = 2581

    ROLE_UNDERLINE = 2582

    WIDTH_DIALOG_SHORTCUT = 240

    WIDTH_DIALOG_VERTICAL = 400

    WIDTH_DIALOG_MINI = 520

    WIDTH_DIALOG_MEDIUM = 960

    WIDTH_DIALOG_LARGE = 1076
