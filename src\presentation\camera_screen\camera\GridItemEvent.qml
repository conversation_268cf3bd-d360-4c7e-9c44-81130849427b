/**
 * GridItemCamera.qml - Specialized camera component kế thừa từ GridItemBase
 *
 * Ch<PERSON><PERSON> năng chính:
 * - <PERSON><PERSON> thừa từ GridItemBase.qml (OOP inheritance)
 * - Video stream rendering và playback thông qua FrameModel
 * - PTZ controls support (pan, tilt, zoom)
 * - Digital zoom functionality với mouse wheel
 * - Camera state indicators và connection status
 * - Camera info overlay với responsive design
 * - Rotation support với snap-to-cardinal-angles
 * - Camera-specific interactions và context menu
 *
 * Architecture:
 * - Inherits: GridItemBase properties, functions, signals
 * - Extends: Camera-specific functionality (PTZ, zoom, rotation)
 * - Overrides: Camera-specific behaviors (fullscreen, selection)
 * - Integration: FrameModel cho video rendering, ConnectionStateOverlay cho status
 */

import QtQuick
import QtQuick.Controls
import QtQuick.Layouts
import models 1.0
import "../controls"
import "../base"
import "../components"
import "."
import "../constants/ZIndexConstants.js" as ZIndex
import '../../../common/qml/map'


GridItemBase {
    id: root
    itemType: "event"
    property bool isOriginImageReady: false
    property bool isCropImageReady: false
    property color header_background: gridModel ? gridModel.get_color_theme_by_key("widget_background_1") : "#2D2D2D"
    property color text_color: gridModel ? gridModel.get_color_theme_by_key("text") : "#2D2D2D"

    Connections {
        target: gridModel
        function onThemeChanged() {
            text_color = gridModel.get_color_theme_by_key("text")
            header_background = gridModel.get_color_theme_by_key("widget_background_1")
        }
    }
    signal cameraSelected()
    signal zoomChanged(real factor)
    signal fullscreenToggled(bool isFullscreen)
    Component.onCompleted: {
    }

    Component.onDestruction: {
    }

    onItemDataChanged: {
        if (itemData) {
        }
    }

    Connections {
        target: itemData
        function onFullscreenChanged() {
        }
    }

    onIsMaximizedChanged: {
        if (isMaximized) {
            fullscreenToggled(true)
        } else {
            resetZoom()
            fullscreenToggled(false)
        }
    }

    property real zoomFactor: 1.0
    property point zoomCenter: Qt.point(0, 0)
    property bool isZooming: false
    function resetZoom() {
        zoomFactor = 1.0
        zoomCenter = Qt.point(root.width / 2, root.height / 2)
        isZooming = false
        zoomChanged(zoomFactor)
    }
    function selectCamera() {
        cameraSelected()
    }

    property string eventTypeName: root.itemData ? root.itemData.eventModel.eventType : ""
    property string timeString: {
        if (!root.itemData || !root.itemData.eventModel.createdAtLocalDate) return ""
        var dateTime = new Date(root.itemData.eventModel.createdAtLocalDate)
        return Qt.formatDateTime(dateTime, "dd/MM/yyyy | hh:mm:ss")
    }
    property string logoPath: {
        var type = eventTypeName ? eventTypeName.toUpperCase() : ""
        if (["RECOGNITION", "PROTECTION", "FREQUENCY", "ACCESS", "MOTION", "TRAFFIC", "HUMAN"].indexOf(type) !== -1)
            return "qrc:/src/assets/ai_icons/ic_recognition_security.svg"
        if (["WEAPON", "UFO", "FIRE"].indexOf(type) !== -1)
            return "qrc:/src/assets/ai_icons/ic_risk_identification.svg"
        return ""
    }

    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 8
        spacing: 4
        // Video display area
        Rectangle {
            id: topRect
            Layout.fillWidth: true
            Layout.fillHeight: true
            Layout.preferredHeight: 300
            color: "transparent"
            radius: 4

            RowLayout {
                anchors.fill: parent
                spacing: 8
                // Ảnh gốc (2/3)
                Rectangle {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    Layout.preferredWidth: 2
                    color: "transparent"
                    border.color: text
                    border.width: 1
                    radius: 4
                    Image {
                        id: originImage
                        anchors.centerIn: parent
                        width: parent.width
                        height: parent.height
                        source: root.itemData ? root.itemData.eventModel.imageFullUrl : ""
                        fillMode: Image.PreserveAspectFit
                        onStatusChanged: {
                            if (status === Image.Ready) {
                                isOriginImageReady = true
                            }
                        }
                    }
                    Text{
                        anchors.fill: parent
                        anchors.margins: 4
                        text: qsTr("Loading Image...")
                        font.pixelSize: Math.max(8, Math.min(12, parent.width / 15))
                        elide: Text.ElideRight
                        font.weight: Font.Medium
                        color: text_color
                        visible: !isOriginImageReady
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                        wrapMode: Text.WordWrap
                        maximumLineCount: 2
                    }
                }
                // Ảnh crop (1/3)
                Rectangle {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    Layout.preferredWidth: 1
                    color: "transparent"
                    border.color: text
                    border.width: 1
                    radius: 4
                    Image {
                        id: cropImage
                        anchors.centerIn: parent
                        width: parent.width
                        height: parent.height
                        source: root.itemData ? root.itemData.eventModel.imageUrl : ""
                        fillMode: Image.PreserveAspectFit
                        onStatusChanged: {
                            if (status === Image.Ready) {
                                isCropImageReady = true
                            }
                        }
                    }
                    Text{
                        anchors.fill: parent
                        anchors.margins: 4
                        text: qsTr("Loading Image...")
                        font.pixelSize: Math.max(8, Math.min(12, parent.width / 15))
                        elide: Text.ElideRight
                        font.weight: Font.Medium
                        color: text_color
                        visible: !isCropImageReady
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                        wrapMode: Text.WordWrap
                        maximumLineCount: 2
                    }
                }
            }
        }
        // Event type row với background và border giống event_bar
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: 50
            color: "transparent"
            border.color: text
            border.width: 1
            radius: 4

            RowLayout {
                id: eventTypeRow
                anchors.fill: parent
                anchors.margins: 10
                spacing: 5

                // Logo (nếu có) - giống như event_bar
                Image {
                    id: eventTypeLogo
                    source: logoPath
                    width: 16
                    height: 16
                    visible: logoPath !== ""
                    fillMode: Image.PreserveAspectFit
                }

                // Tên loại sự kiện - có thể co giãn
                Text {
                    id: eventTypeLabel
                    text: eventTypeName
                    color: text_color
                    font.pixelSize: 18
                    font.bold: true
                    Layout.fillWidth: true
                    horizontalAlignment: Text.AlignLeft
                    verticalAlignment: Text.AlignVCenter
                    elide: Text.ElideRight
                    maximumLineCount: 1
                }

                // Thời gian - tự động co lại khi cần
                Text {
                    id: timeLabel
                    text: timeString
                    color: text_color
                    font.pixelSize: 18
                    font.bold: true
                    Layout.fillWidth: false
                    horizontalAlignment: Text.AlignRight
                    verticalAlignment: Text.AlignVCenter
                    elide: Text.ElideRight
                    maximumLineCount: 1
                    wrapMode: Text.NoWrap
                }
            }
        }

        Rectangle {
            Layout.preferredHeight: 1
            Layout.fillWidth: true
            color: "#444444" // hoặc "gray"
        }
        Rectangle {
            id: bottomRect
            Layout.fillWidth: true
            Layout.fillHeight: true
            Layout.preferredHeight: 240
            color: "transparent"
            RowLayout {
                anchors.fill: parent
                anchors.margins: 8
                spacing: 4
                RowLayout {
                    anchors.fill: parent
                    spacing: 8

                    Rectangle {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        color: "transparent"
                        radius: 4
                        clip: true
                        ColumnLayout {
                            id: colLayout
                            anchors.fill: parent
                            anchors.margins: 4
                            spacing: 4
                            RowLayout  {
                                Layout.fillWidth: true
                                Text {
                                    text: qsTr("Camera")
                                    Layout.preferredWidth: colLayout.width / 4
                                    elide: Text.ElideRight
                                    font.pixelSize: Math.max(8, Math.min(12, colLayout.height / 10))
                                    font.weight: Font.Medium
                                    color: "gray"
                                }
                                Text {
                                    text: root.itemData ? root.itemData.eventModel.cameraName : ""
                                    Layout.preferredWidth: 3*colLayout.width / 4
                                    elide: Text.ElideRight
                                    font.pixelSize: Math.max(8, Math.min(12, colLayout.height / 10))
                                    font.weight: Font.Medium
                                    color: text_color
                                }
                            }
                            RowLayout  {
                                Layout.fillWidth: true
                                Text {
                                    text: qsTr("Type")
                                    Layout.preferredWidth: colLayout.width / 4
                                    elide: Text.ElideRight
                                    font.pixelSize: Math.max(8, Math.min(12, colLayout.height / 10))
                                    font.weight: Font.Medium
                                    color: "gray"
                                }
                                Text {
                                    text: root.itemData ? root.itemData.eventModel.eventType : ""
                                    Layout.preferredWidth: 3*colLayout.width / 4
                                    elide: Text.ElideRight
                                    font.pixelSize: Math.max(8, Math.min(12, colLayout.height / 10))
                                    font.weight: Font.Medium
                                    color: text_color
                                }
                            }
                            RowLayout  {
                                Layout.fillWidth: true
                                Text {
                                    text: qsTr("Object")
                                    Layout.preferredWidth: colLayout.width / 4
                                    elide: Text.ElideRight
                                    font.pixelSize: Math.max(8, Math.min(12, colLayout.height / 10))
                                    font.weight: Font.Medium
                                    color: "gray"
                                }
                                Text {
                                    text: root.itemData ? root.itemData.eventModel.name : ""
                                    Layout.preferredWidth: 3*colLayout.width / 4
                                    elide: Text.ElideRight
                                    font.pixelSize: Math.max(8, Math.min(12, colLayout.height / 10))
                                    font.weight: Font.Medium
                                    color: text_color
                                }
                            }
                            RowLayout  {
                                Layout.fillWidth: true
                                Text {
                                    text: qsTr("Location")
                                    Layout.preferredWidth: colLayout.width / 4
                                    elide: Text.ElideRight
                                    font.pixelSize: Math.max(8, Math.min(12, colLayout.height / 10))
                                    font.weight: Font.Medium
                                    color: "gray"
                                }
                                Text {
                                    text: root.itemData ? root.itemData.eventModel.cameraAddress : ""
                                    Layout.preferredWidth: 3*colLayout.width / 4
                                    elide: Text.ElideRight
                                    font.pixelSize: Math.max(8, Math.min(12, colLayout.height / 10))
                                    font.weight: Font.Medium
                                    color: text_color
                                }
                            }
                            RowLayout  {
                                Layout.fillWidth: true
                                Text {
                                    text: qsTr("Camera group")
                                    Layout.preferredWidth: colLayout.width / 4
                                    elide: Text.ElideRight
                                    font.pixelSize: Math.max(8, Math.min(12, colLayout.height / 10))
                                    font.weight: Font.Medium
                                    color: "gray"
                                }
                                Text {
                                    text: root.itemData ? root.itemData.eventModel.group : ""
                                    Layout.preferredWidth: 3*colLayout.width / 4
                                    elide: Text.ElideRight
                                    font.pixelSize: Math.max(8, Math.min(12, colLayout.height / 10))
                                    font.weight: Font.Medium
                                    color: text_color
                                }
                            }
                            RowLayout  {
                                Layout.fillWidth: true
                                Text {
                                    text: qsTr("Status")
                                    Layout.preferredWidth: colLayout.width / 4
                                    elide: Text.ElideRight
                                    font.pixelSize: Math.max(8, Math.min(12, colLayout.height / 10))
                                    font.weight: Font.Medium
                                    color: "gray"
                                }
                                Text {
                                    text: root.itemData ? root.itemData.eventModel.status : ""
                                    Layout.preferredWidth: 3*colLayout.width / 4
                                    elide: Text.ElideRight
                                    font.pixelSize: Math.max(8, Math.min(12, colLayout.height / 10))
                                    font.weight: Font.Medium
                                    color: text_color
                                }
                            }
                            RowLayout  {
                                Layout.fillWidth: true
                                Text {
                                    text: qsTr("Accuracy")
                                    Layout.preferredWidth: colLayout.width / 4
                                    elide: Text.ElideRight
                                    font.pixelSize: Math.max(8, Math.min(12, colLayout.height / 10))
                                    font.weight: Font.Medium
                                    color: "gray"
                                }
                                Text {
                                    text: root.itemData ? (root.itemData.eventModel.confidence ? root.itemData.eventModel.confidence + "%" : "") : ""
                                    Layout.preferredWidth: 3*colLayout.width / 4
                                    elide: Text.ElideRight
                                    font.pixelSize: Math.max(8, Math.min(12, colLayout.height / 10))
                                    font.weight: Font.Medium
                                    color: text_color
                                }
                            }
                        }

                    }

                    // Đường kẻ dọc giữa hai cột
                    Rectangle {
                        Layout.preferredWidth: 1
                        Layout.fillHeight: true
                        color: "#444444" // hoặc "gray"
                    }

                    Rectangle {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        color: "transparent"
                        radius: 4
                        clip: true
                        ColumnLayout {
                            id: colLayout1
                            anchors.fill: parent
                            anchors.margins: 4
                            RowLayout  {
                                Layout.fillWidth: true
                                Text {
                                    text: qsTr("Gender")
                                    Layout.preferredWidth: colLayout.width / 4
                                    elide: Text.ElideRight 
                                    font.pixelSize: Math.max(8, Math.min(12, colLayout.height / 10))
                                    font.weight: Font.Medium
                                    color: "gray"
                                }
                                Text {
                                    text: root.itemData ? root.itemData.eventModel.Gender : ""
                                    Layout.preferredWidth: 3*colLayout.width / 4
                                    elide: Text.ElideRight 
                                    font.pixelSize: Math.max(8, Math.min(12, colLayout.height / 10))
                                    font.weight: Font.Medium
                                    color: text_color
                                }
                            }
                            RowLayout  {
                                Layout.fillWidth: true
                                Text {
                                    text: qsTr("Age")
                                    font.pixelSize: Math.max(8, Math.min(12, colLayout.height / 10))
                                    font.weight: Font.Medium
                                    color: "gray"
                                    Layout.preferredWidth: colLayout.width / 4
                                    elide: Text.ElideRight 
                                }
                                Text {
                                    text: root.itemData ? root.itemData.eventModel.Age : ""
                                    Layout.preferredWidth: 3*colLayout.width / 4
                                    elide: Text.ElideRight 
                                    font.pixelSize: Math.max(8, Math.min(12, colLayout.height / 10))
                                    font.weight: Font.Medium
                                    color: text_color
                                }
                            }
                            RowLayout  {
                                Layout.fillWidth: true
                                Text {
                                    text: qsTr("Race")
                                    font.pixelSize: Math.max(8, Math.min(12, colLayout.height / 10))
                                    font.weight: Font.Medium
                                    color: "gray"
                                    Layout.preferredWidth: colLayout.width / 4
                                    elide: Text.ElideRight 
                                }
                                Text {
                                    text: root.itemData ? root.itemData.eventModel.Race : ""
                                    Layout.preferredWidth: 3*colLayout.width / 4
                                    elide: Text.ElideRight 
                                    font.pixelSize: Math.max(8, Math.min(12, colLayout.height / 10))
                                    font.weight: Font.Medium
                                    color: text_color
                                }
                            }

                            RowLayout  {
                                Layout.fillWidth: true
                                Text {
                                    text: "Appearance"
                                    font.pixelSize: Math.max(8, Math.min(12, colLayout.height / 10))
                                    font.weight: Font.Medium
                                    color: "gray"
                                    Layout.preferredWidth: colLayout.width / 4
                                    elide: Text.ElideRight 
                                }
                                Text {
                                    text: root.itemData ? root.itemData.eventModel.Appearance : ""
                                    Layout.preferredWidth: 3*colLayout.width / 4
                                    elide: Text.ElideRight 
                                    font.pixelSize: Math.max(8, Math.min(12, colLayout.height / 10))
                                    font.weight: Font.Medium
                                    color: text_color
                                }
                            }
                            RowLayout  {
                                Layout.fillWidth: true
                                Text {
                                    text: qsTr("Vehicle")
                                    font.pixelSize: Math.max(8, Math.min(12, colLayout.height / 10))
                                    font.weight: Font.Medium
                                    color: "gray"
                                    Layout.preferredWidth: colLayout.width / 4
                                    elide: Text.ElideRight 
                                }
                                Text {
                                    text: root.itemData ? root.itemData.eventModel.Vehicle : ""
                                    Layout.preferredWidth: 3*colLayout.width / 4
                                    elide: Text.ElideRight 
                                    font.pixelSize: Math.max(8, Math.min(12, colLayout.height / 10))
                                    font.weight: Font.Medium
                                    color: text_color
                                }
                            }
                            RowLayout  {
                                Layout.fillWidth: true
                                Text {
                                    text: qsTr("Brand")
                                    font.pixelSize: Math.max(8, Math.min(12, colLayout.height / 10))
                                    font.weight: Font.Medium
                                    color: "gray"
                                    Layout.preferredWidth: colLayout.width / 4
                                    elide: Text.ElideRight 
                                }
                                Text {
                                    text: root.itemData ? root.itemData.eventModel.Brand : ""
                                    Layout.preferredWidth: 3*colLayout.width / 4
                                    elide: Text.ElideRight 
                                    font.pixelSize: Math.max(8, Math.min(12, colLayout.height / 10))
                                    font.weight: Font.Medium
                                    color: text_color
                                }
                            }
                            RowLayout  {
                                Layout.fillWidth: true
                                Text {
                                    text: qsTr("Color")
                                    font.pixelSize: Math.max(8, Math.min(12, colLayout.height / 10))
                                    font.weight: Font.Medium
                                    color: "gray"
                                    Layout.preferredWidth: colLayout.width / 4
                                    elide: Text.ElideRight 
                                }
                                Text {
                                    text: root.itemData ? root.itemData.eventModel.Color : ""
                                    Layout.preferredWidth: 3*colLayout.width / 4
                                    elide: Text.ElideRight 
                                    font.pixelSize: Math.max(8, Math.min(12, colLayout.height / 10))
                                    font.weight: Font.Medium
                                    color: text_color
                                }
                            }
                        }

                    }
                }

            }

        }
    }
    Loader {
        id: buttonControlsLoader
        // ✅ CONTENT BOUNDS: Position within content bounds area
        x: root.contentBoundsX
        y: root.contentBoundsY
        width: root.contentBoundsWidth
        height: root.contentBoundsHeight
        z: ZIndex.gridItemControls
        active: shouldShowButtonControls
        asynchronous: true

        // ✅ VISIBILITY LOGIC: Show when hovered/selected but hide during fullscreen animations
        property bool shouldShowButtonControls: {
            // Hide during fullscreen transitions (like border canvas)
            if (root.itemData && root.itemData.isAnimating) return false
            // Show when hovered or selected (even in fullscreen)
            return root.isHovered || root.isSelected
        }

        onActiveChanged: {
        }

        sourceComponent: Component {
            GridItemButtonControls {
                gridItem: root
                itemType: "event"
                anchors.fill: parent

                // ✅ CONTENT BOUNDS: Already positioned within content bounds by loader
                contentBoundsX: 0
                contentBoundsY: 0
                contentBoundsWidth: parent.width
                contentBoundsHeight: parent.height

                onCloseButtonClicked: function(item) {
                    if (root.gridModel) {
                        root.gridModel.isSave = false
                        root.gridModel.removeItemAt(root.gridRow, root.gridCol)
                    }
                }

                onMaximizeButtonClicked: function(item) {
                    // ✅ CENTRALIZED: Use reusable fullscreen handler
                    if (root.itemData && root.animationManager) {
                        var targetState = !root.itemData.fullscreen
                        root.animationManager.handleFullscreenTransition(
                            root, targetState, "EVENT_MAXIMIZE"
                        )
                    }
                }
            }
        }
    }
    ConnectionStateOverlay {
        id: connectionOverlay
        itemData: root.itemData
        isDarkTheme: root.isDarkTheme
        baseZIndex: root.itemData ? root.itemData.zIndex + 5 : 20
    }

}

