# Migration Plan: Current → Multi-Server Authentication System

## 📊 Current Workflow Analysis

### 🔍 **Current Architecture Issues:**

1. **Single Token Storage**: `AuthQSettings` chỉ lưu 1 access_token cho toàn bộ app
2. **No Token Expiry Management**: <PERSON>h<PERSON>ng track thời gian hết hạn token
3. **Manual Refresh**: Mỗi `APIClient` tự handle refresh token riêng lẻ
4. **Hardcoded Server Types**: Chỉ hỗ trợ GPSTECH và CYGATE
5. **No Centralized Management**: Mỗi `Controller` có `APIClient` riêng
6. **WebSocket Token Issues**: Token hết hạn → manual login lại

### 🏗️ **Current Flow:**
```
VMS.py → MainWindow → LoginDialog → Controller → APIClient → 
Login → Save single token → WebSocket → Manual refresh on 401
```

## 🎯 Migration Strategy

### **Phase 1: Foundation Setup** ⚡
**Timeline: 1-2 days**

1. **Install new auth system** (Already done ✅)
   - `src/common/auth/token_manager.py`
   - `src/common/auth/server_registry.py` 
   - `src/common/auth/authenticated_client.py`

2. **Update AuthQSettings** (Already done ✅)
   - Add `save_tokens()`, `get_tokens()`, `delete_tokens()`

3. **Initialize in MainWindow**
   ```python
   # In MainWindow.__init__
   from src.common.auth import server_registry, token_manager
   
   # Setup default servers
   server_registry._setup_default_servers()
   
   # Load saved tokens
   token_manager._load_tokens_from_storage()
   ```

### **Phase 2: Login Dialog Enhancement** 🔐
**Timeline: 2-3 days**

1. **Update LoginDialog UI**
   - Add server type selection dropdown
   - Show server registry options
   - Support custom server input

2. **Modify login logic**
   ```python
   # In LoginDialog.login_clicked()
   def login_clicked(self):
       server_key = self.get_selected_server_key()
       
       if server_type == 'ip_server':
           # Register IP server
           server_key = server_registry.register_ip_server(
               self.server_ip_data, self.server_port_data
           )
           success = True
       else:
           # OAuth login
           success = token_manager.login(
               server_key, self.username_data, self.password_data,
               self.captcha_data, self.captcha_id_data
           )
       
       if success:
           self.create_controller_with_new_system(server_key)
   ```

### **Phase 3: Controller Migration** 🔄
**Timeline: 3-4 days**

1. **Update Controller class**
   ```python
   class Controller(QObject):
       def __init__(self, server_key: str):
           super().__init__()
           self.server_key = server_key
           self.client = client_manager.get_client(server_key)
           self.server_config = server_registry.get_server_config(server_key)
           
       def login(self, parent=None):
           # Login handled by token_manager now
           # Just verify connection
           response = self.client.get('/api/health')
           return response and response.status_code == 200
   ```

2. **Replace APIClient calls**
   ```python
   # Old way
   response = self.api_client.get_cameras()
   
   # New way  
   response = self.client.get('/api/cameras')
   ```

### **Phase 4: WebSocket Integration** 🔌
**Timeline: 2-3 days**

1. **Update WebSocket creation**
   ```python
   # In MainScreen.complete_fetching_data()
   def complete_fetching_data(self, controller):
       server_key = controller.server_key
       
       # Get auth header from token manager
       auth_header = token_manager.get_authorization_header(server_key)
       
       if auth_header:
           headers = {'Authorization': auth_header}
       else:
           headers = {'Id': '123'}  # For IP servers
           
       controller.websocket_event = WebsocketClient(
           url=controller.client.websocket_url,
           header=headers,
           server_ip=controller.server_config.server_ip
       )
   ```

2. **Handle token refresh in WebSocket**
   ```python
   # Connect token manager signals
   token_manager.token_refreshed.connect(self.on_token_refreshed)
   token_manager.login_required.connect(self.on_login_required)
   
   def on_token_refreshed(self, server_key, new_token):
       # Update WebSocket headers
       controller = controller_manager.get_controller_by_key(server_key)
       if controller and controller.websocket_event:
           controller.websocket_event.header['Authorization'] = f'Bearer {new_token}'
   ```

### **Phase 5: Multi-Server Support** 🌐
**Timeline: 2-3 days**

1. **Update ControllerManager**
   ```python
   class ControllerManager:
       def __init__(self):
           self._controllers: Dict[str, Controller] = {}
           
       def add_controller(self, server_key: str, controller: Controller):
           self._controllers[server_key] = controller
           
       def get_controller_by_key(self, server_key: str) -> Controller:
           return self._controllers.get(server_key)
   ```

2. **Update UI to show multiple servers**
   - Server list in sidebar
   - Switch between servers
   - Show connection status per server

### **Phase 6: Testing & Cleanup** 🧪
**Timeline: 2-3 days**

1. **Test scenarios**
   - Login to multiple servers
   - Token refresh on different servers
   - WebSocket reconnection
   - Server disconnection/reconnection

2. **Remove old code**
   - Old APIClient refresh logic
   - Single token storage
   - Hardcoded server handling

## 🔄 **Backward Compatibility**

During migration, support both systems:

```python
# Compatibility wrapper
class LegacyAPIClient:
    def __init__(self, server: ServerInfoModel):
        # Try new system first
        server_key = self._get_server_key(server)
        if server_key:
            self.client = client_manager.get_client(server_key)
            self.use_new_system = True
        else:
            # Fall back to old system
            self.old_client = APIClient(server)
            self.use_new_system = False
    
    def get_cameras(self):
        if self.use_new_system:
            return self.client.get('/api/cameras')
        else:
            return self.old_client.get_cameras()
```

## 📈 **Benefits After Migration**

### ✅ **Immediate Benefits:**
- ✅ Proper OAuth2 token management
- ✅ Automatic token refresh
- ✅ Multi-server support
- ✅ Persistent token storage
- ✅ Better error handling

### ✅ **Long-term Benefits:**
- ✅ Scalable architecture
- ✅ Easy to add new server types
- ✅ Centralized authentication
- ✅ Better security practices
- ✅ Reduced code duplication

## 🚀 **Implementation Priority**

1. **High Priority** (Week 1-2):
   - Phase 1: Foundation Setup
   - Phase 2: Login Dialog Enhancement

2. **Medium Priority** (Week 3-4):
   - Phase 3: Controller Migration
   - Phase 4: WebSocket Integration

3. **Low Priority** (Week 5-6):
   - Phase 5: Multi-Server Support
   - Phase 6: Testing & Cleanup

## 🔧 **Development Tips**

1. **Start Small**: Migrate one server type at a time
2. **Keep Old Code**: Don't delete until new system is stable
3. **Test Frequently**: Each phase should be tested independently
4. **Use Feature Flags**: Toggle between old/new systems during development
5. **Monitor Logs**: Add extensive logging during migration

## 📝 **Success Metrics**

- [ ] All server types working with new system
- [ ] Token refresh working automatically
- [ ] WebSocket reconnection on token refresh
- [ ] Multi-server connections stable
- [ ] No regression in existing functionality
- [ ] Performance equal or better than before
