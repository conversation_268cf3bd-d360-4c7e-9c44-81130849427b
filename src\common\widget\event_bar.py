import logging
import datetime
import json
from PySide6.QtWidgets import <PERSON><PERSON><PERSON><PERSON>, QVBoxLayout, QHBoxLayout, QLabel, QListView, QStyledItemDelegate, QSizePolicy, QPushButton, QDialog, QCheckBox, QApplication, QGridLayout, QFrame
from PySide6.QtGui import QStandardItemModel, QStandardItem, QPainter, QIcon, QColor, QPaintEvent, QPixmap,QDrag,QCursor
from PySide6.QtCore import Qt, QModelIndex, QCoreApplication, QSize, QDateTime, Signal,QThreadPool,QMimeData
from PySide6.QtWidgets import QStyleOptionViewItem, QStyle
from pydantic import Json
from src.common.widget.custom_calendar import CalendarPickerWidget
from src.common.widget.image_widget import ImageWidget
from src.common.widget.search_widget.search_bar import SearchBar
from src.common.model.event_data_model import <PERSON><PERSON>ode<PERSON>, EventD<PERSON>, event_manager
# from src.common.widget.dialog_ai_event_widget import EventDialog
from src.common.slideshow.event_dialog import EventDialog
from src.styles.style import Style
from src.common.controller.main_controller import main_controller,connect_slot
from src.common.controller.controller_manager import controller_manager
from concurrent.futures import ThreadPoolExecutor
from src.common.widget.event.list_button import ListButton, CustomButtonEventType
from src.common.widget.event.event_combobox import EventComboBox
from src.common.widget.event.calendar_combobox import CalendarComboBox
from src.common.widget.dialogs.time_filter_event_dialog import TimeFilterEventDialog
from src.common.widget.dialogs.filter_event_dialog import FilterEventDialog
from src.utils.config import Config
from src.common.widget.event.filter_mode import FilterMode
from typing import List
import pickle
from src.common.model.group_model import group_model_manager
from src.common.model.camera_model import camera_model_manager
from src.common.widget.menus.custom_menus import CustomMenuForEventRightClick
from src.utils.theme_setting import theme_setting
from src.utils.utils import Utils
from src.common.slideshow.slideShow import SlideShow
from src.common.model.camera_model import Camera, CameraModel, camera_model_manager
from src.presentation.device_management_screen.widget.list_custom_widgets import SearchComboBox
from src.common.qml.models.common_enum import CommonEnum
from PySide6.QtCore import Signal
logger = logging.getLogger(__name__)

class CustomStandardItemModel(QStandardItemModel):
    def mimeData(self, indexes):
        logger.debug(f'indexes = {indexes}')
        if len(indexes) == 1:
            mime_data = super(CustomStandardItemModel, self).mimeData(indexes)
            if indexes:
                mime_data.setText(indexes[0].data())
                mime_data.setObjectName(indexes[0].data(Qt.UserRole))
        else:
            mime_data = super(CustomStandardItemModel, self).mimeData(indexes)
            text_list = []
            object_name_list = []
            text_and_user_role_dict = {}
            for index in indexes:
                if index.isValid():
                    text_data = index.data()
                    user_role_data = index.data(Qt.UserRole)
                    text_list.append(text_data)
                    object_name_list.append(user_role_data)
                    text_and_user_role_dict[text_data] = user_role_data
            mime_data.setText("\n".join(text_list))
            byte_array = pickle.dumps(text_and_user_role_dict)
            mime_data.setData("application/multidata", byte_array)
            mime_data.setObjectName("Multi_selection_item")
        return mime_data
    
class EventItemDelegate(QStyledItemDelegate):
    def paint(self, painter: QPainter, option: QStyleOptionViewItem, index: QModelIndex) -> None:
        # Set the background color for the item
        painter.save()
        if option.state & QStyle.State_Selected:
            painter.fillRect(option.rect, option.palette.highlight())
        painter.restore()
        # Call the base class paint method
        super().paint(painter, option, index)


class EventBar(QWidget):
    filter_mode = Signal(dict)
    combobox_hover_signal = Signal(tuple)
    def __init__(self, parent=None):
        super(EventBar, self).__init__(parent)
        main_controller.list_parent['EventBar'] = self
        self.list_screens = QApplication.screens()
        self.text_color = Style.PrimaryColor.white
        self.background_color = Style.PrimaryColor.background
        self._parent = parent
        self.is_search_items = False
        self.text_search = ''
        self.page_idx = 0
        self.event_list_view = None
        self.setup_ui()
        self.setup_stylesheet()
        self.previus_filter_selected = {}
        self.is_reset_event = False
        self.connect_slot()
        self.set_dynamic_stylesheet()
        # Thêm kết nối với theme_change_signal
        main_controller.theme_change_signal.connect(self.set_dynamic_stylesheet)
        
        # Initial check for events - if any exist, hide the message and show the list
        if event_manager.event_list and 0 in event_manager.event_list and event_manager.event_list[0] is not None:
            if (event_manager.event_list[0].data and 
                event_manager.event_list[0].data.content and 
                len(event_manager.event_list[0].data.content) > 0):
                self.hide_not_found_message()
            else:
                self.show_not_found_message()
        else:
            self.show_not_found_message()

    def setup_ui(self):
        # --- ICON + TEXT HEADER ---
        self.header_widget = QWidget()
        header_layout = QHBoxLayout(self.header_widget)
        header_layout.setContentsMargins(8, 8, 8, 0)
        header_layout.setSpacing(8)

        self.header_icon = QLabel()
        self.header_icon.setFixedSize(24, 24)
        self.header_icon.setScaledContents(True)
        self.header_icon.setPixmap(QPixmap(main_controller.get_theme_attribute('Image', 'event_bar_icon')))

        self.header_text = QLabel(self.tr("Realtime events"))
        # Style sẽ được set trong set_dynamic_stylesheet

        header_layout.addWidget(self.header_icon, 0, Qt.AlignmentFlag.AlignVCenter)
        header_layout.addWidget(self.header_text, 0, Qt.AlignmentFlag.AlignVCenter)
        header_layout.addStretch(1)

        # title event number
        event_title_layout = QHBoxLayout()
        event_title_layout.setAlignment(Qt.AlignmentFlag.AlignLeft|Qt.AlignmentFlag.AlignCenter)
        event_title_layout.setContentsMargins(5, 0, 5, 0)
        self.list_button = ListButton()
        self.list_button.add_item(item={
            'object_name': 'alert',
            'icon_on': main_controller.get_theme_attribute('Image', 'alert_on'),
            'icon_off': main_controller.get_theme_attribute('Image', 'alert_off'),
            'title':self.tr("Realtime Events")
        })
        self.list_button.add_item({
            'object_name': 'warning',
            'icon_on': main_controller.get_theme_attribute('Image', 'lightning_on'),
            'icon_off': main_controller.get_theme_attribute('Image', 'lightning_off'),
            'title': self.tr("Warning")
        })
        self.list_button.changed.connect(self.changed_signal)
        # self.list_button.set_number(0,10)
        # self.number_event_widget = QLabel("0")
        # self.number_event_widget.setMinimumWidth(30)
        # self.number_event_widget.setAlignment(Qt.AlignCenter)
        # self.number_event_widget.setWordWrap(True)
        
        # event_title_layout.addWidget(self.number_event_widget)

        # self.event_title_widget = QLabel(self.tr("Real-time events"))
        # event_title_layout.addWidget(self.event_title_widget)
        event_title_layout.addWidget(self.list_button)
        self.server_combobox = SearchComboBox(data=["1","2"],
                                           combobox_clicked=self.server_combobox_clicked)
        # create divider
        # self.divider = QWidget()
        # self.divider.setFixedHeight(1)

        # create filter and history button icon
        self.filter_button = QPushButton()
        self.filter_button.setObjectName("filter_button")
        self.filter_button.setFixedSize(28, 28)
        self.filter_button.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'ic_filter')))
        self.filter_button.setIconSize(QSize(24, 24))
        self.filter_button.setToolTip(self.tr("Filter"))
        self.filter_button.mousePressEvent = lambda event: self.show_filter_dialog(event)

        self.refresh = QPushButton()
        self.refresh.setObjectName("refresh")
        self.refresh.setFixedSize(28, 28)
        self.refresh.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'refresh')))
        self.refresh.setIconSize(QSize(24, 24))
        self.refresh.setToolTip(self.tr("refresh"))
        self.refresh.clicked.connect(self.refresh_clicked)

        self.filter_and_history_button_layout = QHBoxLayout()
        self.filter_and_history_button_layout.setAlignment(
            Qt.AlignmentFlag.AlignRight)
        self.filter_and_history_button_layout.setContentsMargins(5, 0, 5, 0)
        self.filter_and_history_button_layout.setSpacing(2)
        self.filter_and_history_button_layout.addWidget(self.refresh)

        self.filter_and_history_button_layout.addWidget(self.filter_button)

        self.layout_contain_button = QHBoxLayout()
        self.layout_contain_button.setContentsMargins(0, 0, 0, 0)

        self.layout_contain_button.addLayout(self.filter_and_history_button_layout)


        self.filter_event_dialog = FilterEventDialog()

        # create search bar
        self.search_bar = SearchBar(parent=self)
        self.search_bar.search_items_signal.connect(self.search_items)
        self.event_list_view = EventListView(self)
        self.event_list_view.setObjectName("event_list_view")
        self.event_list_view.verticalScrollBar().valueChanged.connect(self.load_more_data)
        self.event_list_view.verticalScrollBar().setStyleSheet(
            f'''    
                QScrollBar:vertical {{
                    background-color: {Style.PrimaryColor.background};
                    width: 10px;
                    margin: 0px 0px 0px 0px;
                }}
                QScrollBar::handle:vertical {{
                    background-color: {Style.PrimaryColor.on_background};
                    border-radius: 5px;
                    min-height: 20px;
                }}
                QScrollBar::add-line:vertical {{
                    background: none;
                }}
                QScrollBar::sub-line:vertical {{
                    background: none;
                }}
                QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {{
                    background: none;
                }}
                QScrollBar::up-arrow:vertical, QScrollBar::down-arrow:vertical {{
                    width: 0px;
                    height: 0px;
                    background: none;
                }}
            '''
        )

        # remove horizontal scrollbar
        # self.event_list_view.setHorizontalScrollBarPolicy(
        #     Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        # set style for horizontal scrollbar
        self.event_list_view.horizontalScrollBar().setStyleSheet(
            f'''
            QScrollBar::horizontal {{
                background-color: {Style.PrimaryColor.background};
                height: 10px;
                margin: 0px 0px 0px 0px;
            }}
            QScrollBar::handle:horizontal {{
                background-color: {Style.PrimaryColor.on_background};
                border-radius: 5px;
                min-width: 20px;
            }}
            QScrollBar::add-line:horizontal {{
                background: none;
            }}
            QScrollBar::sub-line:horizontal {{
                background: none;
            }}
            QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal {{
                background: none;
            }}
            QScrollBar::left-arrow:horizontal, QScrollBar::right-arrow:horizontal {{
                width: 0px;
                height: 0px;
                background: none;
            }}
            '''
        )
        self.layout_search_and_filter = QVBoxLayout()
        self.layout_search_and_filter.setContentsMargins(0, 5, 0, 0)
        self.layout_search_and_filter.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.layout_search_and_filter.addWidget(self.header_widget)
        self.layout_search_and_filter.addWidget(self.search_bar)
        # self.layout_search_and_filter.addLayout(self.layout_contain_button)
        self.main_layout = QVBoxLayout()
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(5)
        # self.main_layout.addLayout(event_title_layout, 1)
        # self.main_layout.addWidget(self.divider)
        # self.main_layout.addWidget(self.server_combobox)
        self.main_layout.addLayout(self.layout_search_and_filter, 5)
        
        # Create a container for the "No results found" message with an icon
        self.not_found_container = QWidget()
        not_found_layout = QVBoxLayout(self.not_found_container)
        not_found_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # Create the icon label
        self.not_found_icon = QLabel()
        self.not_found_icon.setFixedSize(48, 48)
        self.not_found_icon.setScaledContents(True)
        self.not_found_icon.setPixmap(QPixmap(main_controller.get_theme_attribute('Image', 'search_not_found')))
        
        # Create the text label
        self.not_found_label = QLabel(self.tr("No search results"))
        self.not_found_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # Add the icon and text to the layout
        not_found_layout.addWidget(self.not_found_icon, 0, Qt.AlignmentFlag.AlignCenter)
        not_found_layout.addWidget(self.not_found_label, 0, Qt.AlignmentFlag.AlignCenter)
        
        # Add event list view first (will be hidden initially)
        self.main_layout.addWidget(self.event_list_view, 90)
        
        # Add the not found container to the main layout
        self.main_layout.addWidget(self.not_found_container, 90)
        
        # Hide event list view, show not found message by default
        self.event_list_view.setVisible(False)
        self.not_found_container.setVisible(True)
        
        self.background = QWidget()
        self.background.setLayout(self.main_layout)
        self.background.setObjectName("background")

        layout = QHBoxLayout()
        layout.addWidget(self.background)
        layout.setContentsMargins(0, 0, 0, 0)
        self.setLayout(layout)
        self.setObjectName('event_bar_widget')

    def connect_slot(self):
        connect_slot(
            (event_manager.add_event_list_signal,self.add_event_list_signal),
            (event_manager.add_event_signal,self.add_event_signal),
            (self.filter_mode,self.filter_mode_signal))
        
    def setup_stylesheet(self):
        style_sheet_btn = f'''
            QPushButton {{
                background-color: transparent;
                border-radius: 4px;
            }}
            QPushButton:hover {{
                background-color: {main_controller.get_theme_attribute('Color', 'hover_button')};
            }}
            QPushButton:pressed {{    
                background-color: {main_controller.get_theme_attribute('Color', 'hover_button')};
            }}
            '''
        self.filter_button.setStyleSheet(style_sheet_btn)
        self.refresh.setStyleSheet(style_sheet_btn)
        # border left to on_background color
        self.background.setStyleSheet(
            f'''
            #background {{
                background-color: transparent;
                border-left: 1px solid {main_controller.get_theme_attribute('Color','common_border')};
                border-top: 1px solid {main_controller.get_theme_attribute('Color','common_border')};
            }}
            '''
        )
        # self.divider.setStyleSheet(f"background-color: {main_controller.get_theme_attribute('Color','common_border')};")

        self.event_list_view.setStyleSheet(
            f"#event_list_view {{"
            f"background-color: {main_controller.get_theme_attribute('Color', 'main_background')}; "
            f"color: {main_controller.get_theme_attribute('Color', 'text_color_all_app')}; border: none; margin-left: 5px }}"
        )
        # text_search_bar_event self.searchbar
        self.search_bar.set_dynamic_stylesheet()
        
        # Style for the "not found" label
        self.not_found_label.setStyleSheet(
            f"font-size: 14px; "
            f"color: {main_controller.get_theme_attribute('Color', 'text_color_all_app')}; "
            f"padding: 20px;"
        )
    def add_event_signal(self,id):
        event:EventModel = event_manager.get_event(id = id)
        self.insert_event_ai(event)


    def add_event_list_signal(self,data):
        filter_selected = self.filter_event_dialog.filter_selected
        if self.page_idx == 0:
            event_manager.event_list.clear()
            event_manager.event_list[self.page_idx] = event_manager.event_page_list
        else:
            event_manager.event_list[self.page_idx] = event_manager.event_page_list
        
        self.update_list_view_event(event_manager.event_list[self.page_idx])
        
        # Check if there are results after updating
        if self.event_list_view.list_view_model.rowCount() == 0:
            self.show_not_found_message()
        else:
            self.hide_not_found_message()
                            
    def update_list_view_event(self, event_data: EventData = None):
        """Update the list view with event data and handle the 'no results found' message"""
        if not Config.ENABLE_EVENT_BAR:
            return
        
        # Stop any ongoing updates
        main_controller.stop_update_ai_event = False
        
        # Clear the view if reset is requested
        if self.is_reset_event:
            # More efficient clearing of the list view
            self.event_list_view.list_view_model.clear()
            self.is_reset_event = False
        
        # Track if we have valid events to display
        has_events = False
        
        # Process event data if available
        if event_data and event_data.data and event_data.data.content:
            content_count = len(event_data.data.content)
            
            if content_count > 0:
                has_events = True
                # Batch update for better performance
                for event in event_data.data.content:
                    if main_controller.stop_update_ai_event:
                        break
                    self.event_list_view.update_ai_event_below(event)
        
        # Show/hide appropriate UI elements based on event availability
        if has_events:
            self.hide_not_found_message()
        else:
            self.show_not_found_message()

    def get_filter_params(self):
        filter_selected = self.filter_event_dialog.filter_selected
        status = None
        type = None
        if filter_selected[FilterMode.Status][0] == self.tr('All'):
            status = None
        else:
            status = []
            for item in filter_selected[FilterMode.Status]:
                pass
            # tạm thời backend chưa query trường này lên để status = None (get all status)
            status = None
        if filter_selected[FilterMode.AI][0] == self.tr('All'):
            type = None
        else:
            type = []
            for item in filter_selected[FilterMode.AI]:
                pass
            # tạm thời backend chưa query trường này lên để type = None (get all type)
            type = None
        groupCameraIds = None
        if filter_selected[FilterMode.Group][0] == self.tr('All'):
            groupCameraIds = None
        else:
            groupCameraIds = []
            for item in filter_selected[FilterMode.Group]:
                group_model = group_model_manager.get_group_model(name = item)
                if group_model is not None:
                    groupCameraIds.append(group_model.get_property('id'))
        cameraIds = None
        if filter_selected[FilterMode.Camera][0] == self.tr('All'):
            cameraIds = None
        else:
            cameraIds = []
            for item in filter_selected[FilterMode.Camera]:
                camera_model = camera_model_manager.get_camera_model(name = item)
                if camera_model is not None:
                    cameraIds.append(camera_model.id)
                            
        start_date = filter_selected[FilterMode.TimeRange]['start_time']
        end_date = filter_selected[FilterMode.TimeRange]['end_time']
        return {'start_date':start_date, 'end_date': end_date,'cameraIds': cameraIds,'groupCameraIds':groupCameraIds,'type': type, 'status':status}
    
    def refresh_clicked(self):
        self.is_reset_event = True
        self.page_idx = 0
        index = self.list_button.current_index()
        result = self.get_filter_params()
        isWarningConfig = 2 if index == 0 else 1
        # main_controller.get_events(parent=self, page=self.page_idx, size=Utils.MaxEvent,
        #                           dateFrom=result['start_date'],dateTo=result['end_date'],
        #                           cameraIds=result['cameraIds'],groupCameraIds=result['groupCameraIds'], 
        #                           isWarningConfig=isWarningConfig,status=result['status'],type=result['type'])
        
        # If there's no data after refresh, ensure message is shown
        if self.event_list_view.list_view_model.rowCount() == 0:
            self.show_not_found_message()

    def filter_mode_signal(self,data):
        self.is_reset_event = True
        self.page_idx = 0
        if self.previus_filter_selected != data:
            self.previus_filter_selected = data
            logger.debug(f"filter_mode_signal = {data}")
            index = self.list_button.current_index()
            # filter_selected = self.filter_event_dialog.filter_selected
            result = self.get_filter_params()
            isWarningConfig = 2 if index == 0 else 1
            main_controller.get_events(parent=self, page=self.page_idx, size=Utils.MaxEvent,dateFrom = result['start_date'],dateTo = result['end_date'],cameraIds=result['cameraIds'],groupCameraIds=result['groupCameraIds'], isWarningConfig=isWarningConfig,status=result['status'],type=result['type'])
    def server_combobox_clicked(self, index):
        pass
    def changed_signal(self,index):
        self.is_reset_event = True
        self.page_idx = 0
        result = self.get_filter_params()
        if index == 0:
            main_controller.get_events(parent=self, page=self.page_idx, size=Utils.MaxEvent,dateFrom = result['start_date'],dateTo = result['end_date'],cameraIds=result['cameraIds'],groupCameraIds=result['groupCameraIds'], isWarningConfig=2,status=result['status'],type=result['type'])

        elif index == 1:
            main_controller.get_events(parent=self, page=self.page_idx, size=Utils.MaxEvent,dateFrom = result['start_date'],dateTo = result['end_date'],cameraIds=result['cameraIds'],groupCameraIds=result['groupCameraIds'], isWarningConfig=1,status=result['status'],type=result['type'])
            
    def create_event_data(self):
        self.is_reset_event = True
        start_date = datetime.date.today().strftime("%Y-%m-%d 00:00:00")
        end_date = datetime.date.today().strftime("%Y-%m-%d 23:59:59")
        main_controller.get_events(parent=self, page=self.page_idx, size=Utils.MaxEvent,dateFrom = start_date,dateTo = end_date,isWarningConfig = 2)

    def combobox_hover(self, data):
        self.combobox_hover_signal.emit(data)

    def load_more_data(self):
        scroll_bar = self.event_list_view.verticalScrollBar()
        max_value = scroll_bar.maximum()
        current_value = scroll_bar.value()
        
        if current_value == max_value:
            # Increment page index for next batch of data
            next_page_idx = self.page_idx + 1
            
            # Check if we already have data for the next page in event_manager
            if next_page_idx in event_manager.event_list and event_manager.event_list[next_page_idx] is not None:
                self.page_idx = next_page_idx
                event_data = event_manager.event_list[self.page_idx]
                
                # If we're doing a search, filter the next page's content
                if self.text_search != '':
                    if event_data and event_data.data and event_data.data.content:
                        filtered_content = []
                        for event in event_data.data.content:
                            # Apply same search filter as in search_items
                            if (self.text_search.lower() in event.name.lower() or 
                                self.text_search.lower() in event.cameraName.lower() or
                                self.text_search.lower() in event.type.lower()):
                                filtered_content.append(event)
                        
                        # Add filtered events to the view
                        for event in filtered_content:
                            self.event_list_view.update_ai_event_below(event)
                else:
                    # If not searching, just add all events from the next page
                    if event_data and event_data.data and event_data.data.content:
                        for event in event_data.data.content:
                            self.event_list_view.update_ai_event_below(event)
            else:
                # If we don't have the next page data already, we need to fetch it with API
                # This is commented out as per your request to use only existing data
                # index = self.list_button.current_index()
                # result = self.get_filter_params()
                # isWarningConfig = 2 if index == 0 else 1
                # main_controller.get_events(...)
                pass

    def insert_event_ai(self, event: EventModel):
        # logger.debug(f"Inserting event = {event}")
        if not self.is_search_items:
            # case listview dang khong o trang thai search
            if self.event_list_view.list_view_model.rowCount() > Utils.MaxEvent:
                self.event_list_view.remove_ai_event_below()
            self.event_list_view.update_ai_event_above(event)
            # Hide the "no results" message since we've added an event
            self.hide_not_found_message()
        else:
            # case listview dang o trang thai search
            if self.text_search != '':
                if self.text_search.lower() in event.get_property("soCmt").lower():
                    self.event_list_view.remove_ai_event_below()
                    self.event_list_view.update_ai_event_above(event)
                    # Hide the "no results" message since we've added an event
                    self.hide_not_found_message()

    def search_items(self, text):
        main_controller.stop_update_ai_event = True
        self.text_search = text
        self.is_reset_event = True
        
        # Clear the current view
        for index in range(self.event_list_view.model().rowCount()):
            widget = self.event_list_view.indexWidget(
                self.event_list_view.model().index(index, 0))
            if widget is not None:
                widget.deleteLater()
        self.event_list_view.list_view_model.clear()
        
        if text != '':
            # Search in existing data without making API call
            found_events = False
            
            # Check if there are events in the event_manager
            for page_idx, event_data in event_manager.event_list.items():
                if event_data and event_data.data and event_data.data.content:
                    filtered_content = []
                    
                    # Filter events based on search text
                    for event in event_data.data.content:
                        # Search in relevant event fields (name, cameraName, etc.)
                        if (text.lower() in event.name.lower() or 
                            text.lower() in event.cameraName.lower() or
                            text.lower() in event.type.lower()):
                            filtered_content.append(event)
                            found_events = True
                    
                    # Display filtered events
                    for event in filtered_content:
                        self.event_list_view.update_ai_event_below(event)
            
            # Show/hide "no results" message
            if not found_events:
                self.show_not_found_message()
            else:
                self.hide_not_found_message()
        else:
            # If search text is empty, show all events from the first page
            if 0 in event_manager.event_list and event_manager.event_list[0] is not None:
                self.update_list_view_event(event_manager.event_list[0])
                self.hide_not_found_message()
            else:
                # If no events exist, show the not found message
                self.show_not_found_message()
        
        self.is_reset_event = False

    def update_ai_event(self, event: EventModel):
        self.insert_event_ai(event)


    def retranslateUi(self):
        self.search_bar.retranslateUi_searchbar()
        self.header_text.setText(self.tr('Realtime Events'))
        for index in range(self.event_list_view.list_view_model.rowCount()):
            item = self.event_list_view.list_view_model.item(index, 0)
            item.main_widget.retransUi()
        for key, value in self.list_button.list_action.items():
            if key == 0:
                widget_button: CustomButtonEventType = value.get('widget')
                widget_button.title_label.setText(self.tr('Realtime Events'))
            elif key == 1:
                widget_button: CustomButtonEventType = value.get('widget')
                widget_button.title_label.setText(self.tr('Warning'))
        
        # Add translation for not found label
        self.not_found_label.setText(self.tr("No search results"))

    def restyle_even_bar(self):
        self.filter_button.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'ic_filter')))
        self.refresh.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'refresh')))
        self.filter_event_dialog.setup_dynamic_style_sheet()
        self.setup_stylesheet()
        self.search_bar.set_dynamic_stylesheet()
        for key, value in self.list_button.list_action.items():
            if key == 0:
                widget_button: CustomButtonEventType = value.get('widget')
                widget_button.restyle_button(icon_on=main_controller.get_theme_attribute('Image', 'alert_on'),
                                             icon_off=main_controller.get_theme_attribute('Image', 'alert_off'))
            else:
                widget_button: CustomButtonEventType = value.get('widget')
                widget_button.restyle_button(icon_on=main_controller.get_theme_attribute('Image', 'lightning_on'),
                                             icon_off=main_controller.get_theme_attribute('Image', 'lightning_off'))

        # Update the no results icon for theme changes
        self.not_found_icon.setPixmap(QPixmap(main_controller.get_theme_attribute('Image', 'search_not_found')))

    def show_filter_dialog(self,event):
        position = event.globalPos()
        # button_pos = self.filter_button.mapToGlobal(
        #     self.filter_button.rect().bottomLeft())
        self.filter_event_dialog.showAt(position)

    def show_not_found_message(self):
        """Show a message with icon when no search results are found"""
        # Hide the list view
        if self.event_list_view:
            self.event_list_view.setVisible(False)
        
        # Style the message
        self.not_found_label.setStyleSheet(
            f"font-size: 14px; "
            f"color: {main_controller.get_theme_attribute('Color', 'text_color_all_app')}; "
            f"padding: 10px;"
        )
        
        # Show the not found container
        self.not_found_container.setVisible(True)

    def hide_not_found_message(self):
        """Hide the not found message and show the list view"""
        if self.event_list_view:
            self.event_list_view.setVisible(True)
        
        self.not_found_container.setVisible(False)

    def set_dynamic_stylesheet(self):
        color_text = main_controller.get_theme_attribute('Color','text')
        # Style cho header_text
        if hasattr(self, 'header_text'):
            self.header_text.setStyleSheet(f"font-size: 18px; font-weight: bold; color: {color_text};")
        # Cập nhật icon cho header_icon theo theme
        if hasattr(self, 'header_icon'):
            self.header_icon.setPixmap(QPixmap(main_controller.get_theme_attribute('Image', 'event_bar_icon')))

        # Bỏ viền cho image_crop
        if hasattr(self, 'image_crop'):
            self.image_crop.setStyleSheet("border: none; border-radius: 0px;")


class EventListView(QListView):
    def __init__(self, parent=None):
        super(EventListView, self).__init__(parent)
        self.event_bar: EventBar = parent
        self.thread_pool = ThreadPoolExecutor(max_workers=4)
        self.setObjectName("event_list_view")
        self.setup_ui()
        self.setup_stylesheet()

    def setup_ui(self):
        # Set the custom delegate for item view
        self.setItemDelegate(EventItemDelegate())
        self.list_view_model = QStandardItemModel()
        self.setModel(self.list_view_model)
        self.setSpacing(2)
        
        # disable selection
        self.setSelectionMode(QListView.SelectionMode.NoSelection)
        # disable focus
        self.setFocusPolicy(Qt.FocusPolicy.NoFocus)

    def setup_stylesheet(self):
        pass

    def update_ai_event_above(self, event: EventModel):
        item = EventItem(event=event, thread_pool=self.thread_pool)
        
        self.list_view_model.insertRow(0, item)
        self.setIndexWidget(self.list_view_model.index(0, 0), item.main_widget)
        # if self.event_bar.main_controller.events_ai_today != None:
        #     count = self.event_bar.main_controller.events_ai_today.total_results
        #     # self.event_bar.number_event_widget.setText(f'{count}')

    def remove_ai_event_below(self):
        widget = self.indexWidget(self.list_view_model.index(
            self.list_view_model.rowCount() - 1, 0))
        if widget is not None:
            widget.deleteLater()
            self.list_view_model.removeRow(self.list_view_model.rowCount() - 1)

    def update_ai_event_below(self, event: EventModel):
        item = EventItem(event=event, thread_pool=self.thread_pool)
        self.list_view_model.appendRow(item)
        self.setIndexWidget(self.list_view_model.index(
            self.list_view_model.rowCount() - 1, 0), item.main_widget)
        # count = self.event_bar.main_controller.events_ai_today.total_results
        # self.event_bar.number_event_widget.setText(f'{count}')

class ChildEventWidget(QWidget):
    expandedChanged = Signal(int)  # emit new height
    def __init__(self, parent=None, event: EventModel = None, thread_pool: ThreadPoolExecutor = None):
        super().__init__(parent)
        self.thread_pool = thread_pool
        self.text_color = Style.PrimaryColor.white
        self.background_color = Style.PrimaryColor.background
        self.event_ai = event
        self.name = event.name
        self.time = event.get_property("createdAtLocalDate")
        self.ori_image_path = event.get_property("imageFullUrl")
        # self.crop_image_path = event.crop_image
        self.event_type = event.aiType
        self.camera_name = event.get_property("cameraName")
        self.warning_Config = event.get_property("warningConfig")
        self.camera_location = event.get_property("cameraLocation")
        self.status1 = event.get_property("status")
        self.is_on_clicked = False
        # self.event_warning_type = event.warning_type
        # self.cameraCheckInApply = False
        # self.cameraCheckOutApply = False
        self.event_width = self.width()
        self.drag = None
        self._parent = parent
        self.setup_ui()
        main_controller.theme_change_signal.connect(self.set_dynamic_stylesheet)
        self.set_dynamic_stylesheet()

    def setup_ui(self):
        layout = QHBoxLayout()
        layout.setContentsMargins(0,0,0,0)
        self.setLayout(layout)

        self.main_widget = QWidget()
        layout.addWidget(self.main_widget)
        self.main_layout = QVBoxLayout(self.main_widget)
        self.main_layout.setContentsMargins(0,0,0,0)
        self.main_layout.setSpacing(0)

        # 1. Image at the top, rounded corners
        image_layout = QHBoxLayout()
        image_layout.setContentsMargins(0, 0, 0, 0)
        image_layout.setSpacing(0)
        image_layout.setAlignment(Qt.AlignmentFlag.AlignHCenter)
        self.image_crop = ImageWidget(event_ai=self.event_ai,
            height=170, allow_click_to_show_origin=False, thread_pool=self.thread_pool, width_image=320,is_event_bar=True)
        self.image_crop.mousePressEvent = self.mousePressEvent
        self.image_crop.mouseMoveEvent = self.mouseMoveEvent
        image_layout.addWidget(self.image_crop)
        self.main_layout.addLayout(image_layout)

        # 2. Event type + time row in a rounded QWidget (with inner container)
        event_type_row = QWidget()
        event_type_row.setObjectName("event_type_row")
        event_type_row.setFixedHeight(50)
        event_type_layout = QVBoxLayout(event_type_row)
        event_type_layout.setContentsMargins(0, 0, 0, 0)
        event_type_layout.setSpacing(0)

        # Inner container (no border, no background)
        inner = QWidget()
        inner.setStyleSheet("background: transparent; border: none;")
        inner_layout = QHBoxLayout(inner)
        inner_layout.setContentsMargins(10, 0, 10, 0)  # Lề trái/phải 10px cho đẹp
        inner_layout.setSpacing(0)

# Logo
        event_type_name = self.event_ai.get_property('eventType') or self.event_ai.get_property('type', '')
        logo_path = None
        type_upper = str(event_type_name).upper()
        recognition_types = [
            'RECOGNITION', 'PROTECTION', 'FREQUENCY', 'ACCESS', 'MOTION', 'TRAFFIC', 'HUMAN'
        ]
        risk_types = [
            'WEAPON', 'UFO', 'FIRE'
        ]
        if type_upper in recognition_types:
            logo_path = main_controller.get_theme_attribute('Image', 'ic_recognition_security')
        elif type_upper in risk_types:
            logo_path = main_controller.get_theme_attribute('Image', 'ic_risk_identification')
        if logo_path:
            self.event_type_logo = QLabel()
            pixmap = QPixmap(logo_path).scaled(16, 16, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            self.event_type_logo.setPixmap(pixmap)
            inner_layout.addWidget(self.event_type_logo, alignment=Qt.AlignVCenter)

        # Type
        self.event_type_label = QLabel(str(event_type_name))
        self.event_type_label.setSizePolicy(QSizePolicy.MinimumExpanding, QSizePolicy.Fixed)
        self.event_type_label.setWordWrap(False)
        self.event_type_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.event_type_label.setMinimumHeight(16)
        # --- BỔ SUNG: cập nhật dynamic stylesheet cho event_type_label ---
        if hasattr(self, "event_type_label"):
            self.event_type_label.setStyleSheet(
                f"color: {main_controller.get_theme_attribute('Color','text')}; font-size: 11px; font-weight: 600; margin: 0; padding: 0;"
            )
        inner_layout.addWidget(self.event_type_label, alignment=Qt.AlignVCenter)

        # Time
        try:
            local_datetime = datetime.datetime.fromisoformat(self.time)
            local_timestamp = local_datetime.strftime("%d/%m/%Y | %H:%M:%S")
        except ValueError:
            try:
                local_datetime = datetime.datetime.strptime(self.time, "%Y-%m-%d %H:%M:%S.%f")
                local_timestamp = local_datetime.strftime("%d/%m/%Y | %H:%M:%S")
            except ValueError:
                local_timestamp = datetime.datetime.now().strftime("%d/%m/%Y | %H:%M:%S")
                logger.error(f"Failed to parse datetime: {self.time}")
        # Khởi tạo time_label NGAY SAU khi có local_timestamp
        self.time_label = QLabel(local_timestamp)
        self.time_label.setSizePolicy(QSizePolicy.Minimum, QSizePolicy.Fixed)
        self.time_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.time_label.setMinimumHeight(24)
        self.time_label.setMinimumWidth(120)
        self.time_label.setStyleSheet(
            f"color: {main_controller.get_theme_attribute('Color','text')}; font-size: 11px; font-weight: 600; margin: 0; padding: 0; text-overflow: ellipsis; overflow: hidden; white-space: nowrap;"
        )
        inner_layout.addWidget(self.time_label, alignment=Qt.AlignRight | Qt.AlignVCenter)
        event_type_layout.addWidget(inner)
        self.main_layout.addWidget(event_type_row)
        self.event_type_row = event_type_row

        # 3. Info table in a rounded QWidget
        info_bg = QWidget()
        info_bg.setObjectName("info_bg")
        info_bg.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        info_grid = QGridLayout(info_bg)
        info_grid.setContentsMargins(0, 0, 0, 0)
        info_grid.setHorizontalSpacing(13)
        info_grid.setVerticalSpacing(6)
        labels = [
            (self.tr("Camera"), self.camera_name or ""),
            (self.tr("Object"), self.name or ""),
            (self.tr("Location"), self.event_ai.get_property("cameraAddress", "")),
            (self.tr("Camera Group"), self.event_ai.get_property("group", "")),
            (self.tr("Status"), self.event_ai.get_property("status")),
            (self.tr("Confidence"), str(self.event_ai.get_property("confidence", ""))),
            (self.tr("Gender"), self.event_ai.get_property("humanInfo.face.gender", "")),
            (self.tr("Age"), str(self.event_ai.get_property("humanInfo.face.age", ""))),
            (self.tr("Race"), self.event_ai.get_property("humanInfo.face.race", "")),
            (self.tr("Appearance"), self.event_ai.get_property("humanInfo.person.hair", "")),
            (self.tr("Vehicle"), self.event_ai.get_property("vehicle", "")),
            (self.tr("Brand"), self.event_ai.get_property("brand", "")),
            (self.tr("Color"), self.event_ai.get_property("color", "")),
        ]
        self.info_labels = []
        self.info_values = []
        self._info_grid_widgets = []
        for row, (label, value) in enumerate(labels):
            label_widget = QLabel(label)
            label_widget.setMaximumWidth(120)
            value_widget = QLabel(value)
            value_widget.setWordWrap(True)
            label_widget.setAlignment(Qt.AlignTop | Qt.AlignLeft)
            value_widget.setAlignment(Qt.AlignTop | Qt.AlignLeft)
            self.info_labels.append(label_widget)
            self.info_values.append(value_widget)
            info_grid.addWidget(label_widget, row, 0)
            info_grid.addWidget(value_widget, row, 1)
            self._info_grid_widgets.append((label, label_widget, value_widget))
        self.main_layout.addWidget(info_bg)
        self.info_bg = info_bg

        # 4. Expand/collapse button at the bottom, centered
        self.expand_button = QPushButton("Show more")
        self.expand_button.setFixedHeight(32)
        self.expand_button.setFixedWidth(92)
        self.expand_button.clicked.connect(self.toggle_expand)
        button_layout = QHBoxLayout()
        button_layout.setContentsMargins(0, 0, 0, 8)  # Thêm padding dưới 16px
        button_layout.setAlignment(Qt.AlignmentFlag.AlignHCenter)
        button_layout.addWidget(self.expand_button)
        self.main_layout.addLayout(button_layout)
        self._is_expanded = False
        # Set default to collapsed: hide image and all info except Camera, Đối tượng, Vị trí, and event_type_row
        self.image_crop.setMaximumHeight(0)
        self.image_crop.setVisible(False)
        self.event_type_row.setVisible(True)
        self.info_bg.setVisible(True)

        for label, label_widget, value_widget in getattr(self, '_info_grid_widgets', []):
            if label in (self.tr("Camera"), self.tr("Object"), self.tr("Location")):
                label_widget.setVisible(True)
                value_widget.setVisible(True)
            else:
                label_widget.setVisible(False)
                value_widget.setVisible(False)
        self.set_dynamic_stylesheet()

    def toggle_expand(self):
        if not self._is_expanded:

            print("=== EVENT DATA ===")
            print(self.event_ai.data)
            new_height = self.height()*3.95
            self.expand_button.setText("Show less")
            self.image_crop.setMaximumHeight(170)
            self.image_crop.setVisible(True)
            self.event_type_row.setVisible(True)
            self.info_bg.setVisible(True)

            for label, label_widget, value_widget in getattr(self, '_info_grid_widgets', []):
                label_widget.setVisible(True)
                value_widget.setVisible(True)
        else:
            new_height = 238
            self.expand_button.setText("Show more")
            self.image_crop.setMaximumHeight(0)
            self.image_crop.setVisible(False)
            self.event_type_row.setVisible(True)
            self.info_bg.setVisible(True)
            for label, label_widget, value_widget in getattr(self, '_info_grid_widgets', []):
                if label in (self.tr("Camera"), self.tr("Object"), self.tr("Location")):
                    label_widget.setVisible(True)
                    value_widget.setVisible(True)
                else:
                    label_widget.setVisible(False)
                    value_widget.setVisible(False)
        self._is_expanded = not self._is_expanded
        self.expandedChanged.emit(new_height)

    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.drag = QDrag(self)
            mime_data = QMimeData()
            mime_data.setObjectName("event")
            mime_data.setText(str(self.event_ai.id))
            data = {"id": self.event_ai.id,"type": "event"}
            mime_data.setData('application/event', json.dumps(data).encode())
            self.drag.setMimeData(mime_data)
            # show dialog
            if main_controller.event_selected != self:
                try:
                    main_controller.event_selected.unclicked()
                except Exception as e:
                    logger.debug(f'on_item_click error: {e}')
                self.clicked()
                main_controller.event_selected = self
        elif event.button() == Qt.RightButton:
            mouse_position = QCursor.pos()
            self.showMenu(mouse_position)

    def mouseMoveEvent(self, event):
        if self.drag is not None:
            # Execute the drag operation
            self.drag.exec()

    def showMenu(self, position):
        self.main_menu = CustomMenuForEventRightClick(event_ai=self.event_ai)
        self.main_menu.open_new_tab_signal.connect(self.event_open_new_tab)
        self.main_menu.open_in_tab_signal.connect(self.event_open_in_tab)

        self.main_menu.exec_(position)

    def event_open_in_tab(self, data):
        id, tab_name, row, col = data
        # logger.debug(f'data = {data}')
        event_ai = event_manager.get_event(id)
        # tab_model = tab_model_manager.get_tab_model(tab_name=tab_name)
        # if event_ai is not None and tab_model is not None:
            
        #     for index,grid_item in tab_model.data.listGridData.items():
        #         # logger.debug(f'data1 = {grid_item.index,grid_item.row,grid_item.col}')
        #         if grid_item.row == row and grid_item.col == col:
        #             # logger.debug(f'data = {grid_item}')
        #             grid_item.type = CommonEnum.ItemType.EVENT
        #             tab_model.set_model(grid_item = grid_item,model = event_ai)
        #             tab_model.add_grid_item_signal.emit(grid_item.index)
        #             break

    def event_open_new_tab(self, data):
        camera_screen = main_controller.list_parent['CameraScreen']
        if camera_screen is not None:
            camera_screen.event_open_new_tab(data)
        logger.debug(f"event_open_new_tab = {data,camera_screen}")

    def create_ai_label(self):
        if self.event_type == 'VEHICLE':
            vehicle_path = QPixmap(main_controller.get_theme_attribute('Image', 'vehicle_detection_on'))
            return vehicle_path.scaledToHeight(16)
        elif self.event_type == 'HUMAN':
            human_path = QPixmap(main_controller.get_theme_attribute('Image', 'face_recognition_on'))
            return human_path.scaledToHeight(16)
        elif self.event_type == 'CROWD':
            crowd_path = QPixmap(main_controller.get_theme_attribute('Image', 'crowd_detection_on'))
            return crowd_path.scaledToHeight(16)
        elif self.event_type == 'ACCESS_CONTROL':
            access_control_path = QPixmap(main_controller.get_theme_attribute('Image', 'access_control_on'))
            return access_control_path.scaledToHeight(16)
        return None
    
    def set_dynamic_stylesheet(self):
        # styleName: Caption/Medium;
        # font-family: Inter;
        # font-size: 10px;
        # font-weight: 500;
        # line-height: 14px;
        # letter-spacing: 0.01em;
        # text-align: left;
        

        # Remove or update references to self.event_name_label in set_dynamic_stylesheet
        # Only set style for widgets that exist (event_time_label, etc.)
        color_text = main_controller.get_theme_attribute('Color','text')
        self.expand_button.setStyleSheet(
            f"""
            margin-bottom: 8px;
            border-radius: 6px;
            border: 1.5px solid {color_text};
            font-size: 11px;
            text-align: center;
            color: {color_text};
            background: transparent;
            """
        )
        self.time_label.setStyleSheet(f"color: {color_text}; font-size: 13px; font-weight: 600; text-overflow: ellipsis; overflow: hidden; white-space: nowrap;")
        # --- BỔ SUNG STYLE CHO info_bg, label_widget, value_widget ---
        if hasattr(self, "info_bg"):
            self.info_bg.setStyleSheet(
                "background: transparent; border: 0; margin: 0;"
            )
            self.info_bg.setContentsMargins(5, 8, 5, 8)
        for label_widget in getattr(self, 'info_labels', []):
            label_widget.setStyleSheet(
                "font-weight: 600; margin: 0; padding: 0;" +
                f" color: {color_text};"
            )
        for value_widget in getattr(self, 'info_values', []):
            value_widget.setStyleSheet(
                "margin: 0; padding: 0;" +
                f" color: {color_text};"
            )
        # --- BỔ SUNG: cập nhật styleSheet cho event_type_row ---
        if hasattr(self, "event_type_row"):
            self.event_type_row.setStyleSheet(
                "background: transparent;" +
                f"border: 1.5px solid {main_controller.get_theme_attribute('Color', 'border_item')};" +
                "border-radius: 8px; padding: 0; margin: 8px 12px 8px 12px;"
            )
        # --- BỔ SUNG: border cho toàn bộ ChildEventWidget ---
        border_color = main_controller.get_theme_attribute('Color', 'border_item')
        self.setStyleSheet(f"border: 1.5px solid {border_color}; border-radius: 8px;")
        # --- BỔ SUNG: cập nhật dynamic stylesheet cho label_loading ---
        if hasattr(self.image_crop, "label_loading"):
            self.image_crop.label_loading.setStyleSheet(
                f"color: {color_text}; font-size: 12px;"
            )
        # --- BỔ SUNG: cập nhật dynamic stylesheet cho event_type_label ---
        if hasattr(self, "event_type_label"):
            self.event_type_label.setStyleSheet(
                f"color: {color_text}; font-size: 11px; font-weight: 600; margin: 0; padding: 0;"
            )
        # Style cho header_text
        if hasattr(self, 'header_text'):
            self.header_text.setStyleSheet(f"font-size: 18px; font-weight: bold; color: {color_text};")
        # Cập nhật icon cho header_icon theo theme
        if hasattr(self, 'header_icon'):
            self.header_icon.setPixmap(QPixmap(main_controller.get_theme_attribute('Image', 'event_bar_icon')))

    def clicked(self):
        self.is_on_clicked = True
        self.setStyleSheet(f"border: 1.5px solid {main_controller.get_theme_attribute('Color','text_pressed')};border-radius: 8px;")

    def unclicked(self):
        self.is_on_clicked = False
        self.setStyleSheet(f"border: 1.5px solid {main_controller.get_theme_attribute('Color', 'border_item')};border-radius: 8px;")

    def on_item_click(self, event):
        # <PySide6.QtGui.QMouseEvent(MouseButtonPress LeftButton pos=100,25 scn=1742,61 gbl=1742,114 dev=QPointingDevice("core pointer" Mouse id=1))>
        if event.button() == Qt.LeftButton:
            # show dialog
            if main_controller.event_selected != self:
                try:
                    main_controller.event_selected.unclicked()
                except Exception as e:
                    logger.debug(f'on_item_click error: {e}')
                self.clicked()
                main_controller.event_selected = self

    def dragEnterEvent(self, event):
        event.acceptProposedAction()

    def retransUi(self):
        # Cập nhật lại các label info khi đổi ngôn ngữ
        label_keys = [
            "Camera", "Object", "Location", "Camera Group", "Status", "Confidence",
            "Gender", "Age", "Race", "Appearance", "Vehicle", "Brand", "Color"
        ]
        new_info_grid_widgets = []
        for i, (label, label_widget, value_widget) in enumerate(self._info_grid_widgets):
            if i < len(label_keys):
                new_label = self.tr(label_keys[i])
                label_widget.setText(new_label)
                new_info_grid_widgets.append((new_label, label_widget, value_widget))
            else:
                new_info_grid_widgets.append((label, label_widget, value_widget))
        self._info_grid_widgets = new_info_grid_widgets

        self.event_type_label.setText(self.status1)
        self.image_crop.label_loading.setText(self.tr('Loading...'))
class EventItem(QStandardItem):
    def __init__(self, parent=None, event: EventModel = None, thread_pool: ThreadPoolExecutor = None):
        super(EventItem, self).__init__(parent)
        self.main_widget = ChildEventWidget(event=event, thread_pool=thread_pool)
        self.setSizeHint(QSize(-1, 238))
        self.main_widget.expandedChanged.connect(self.on_expanded_changed)
    def on_expanded_changed(self, new_height):
        self.setSizeHint(QSize(-1, new_height))
        # Force the view to update its layout if possible
        parent_view = self.main_widget.parent()
        while parent_view is not None and not hasattr(parent_view, 'updateGeometries'):
            parent_view = parent_view.parent()
        if parent_view is not None and hasattr(parent_view, 'updateGeometries'):
            parent_view.updateGeometries()


# if __name__ == "__main__":
#     import sys
#     from PySide6.QtWidgets import QApplication

#     app = QApplication(sys.argv)
#     window = EventBar()
#     window.show()
#     sys.exit(app.exec_())

